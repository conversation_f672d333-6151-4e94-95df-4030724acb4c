<?php

use App\Models\Pass;
use Illuminate\Support\Facades\Artisan;
use PKPass\PKPass;

/*
|--------------------------------------------------------------------------
| Console Routes
|--------------------------------------------------------------------------
|
| This file is where you may define all of your Closure based console
| commands. Each Closure is bound to a command instance allowing a
| simple approach to interacting with each command's IO methods.
|
*/

Artisan::command('pass', function ()
{
    $project = \App\Models\Project::first();
    $certificate = $project->certificate;

    $certificate->withFiles(function ($pemPath, $p12Path) use ($certificate, &$file) {
        $pass = new PKPass($p12Path, '');

        $serial = '4Hg9MPyhErG9XHLyo1EM1q';

        $jayParsedAry = [
            "description" => "description",
            "formatVersion" => 1,
            "organizationName" => "No company",
            "passTypeIdentifier" => $certificate->pass_type_id,
            "serialNumber" => $serial,
            "teamIdentifier" => $certificate->team_id,
            "storeCard" => [
                "backFields" => [
                    [
                        "changeMessage" => "universal.info.cm",
                        "key" => "universal.info",
                        "label" => "universal.info.label",
                        "value" => "universal.info.value",
                        "attributedValue" => "universal.info.value",
                        "dataDetectorTypes" => [
                            "PKDataDetectorTypeCalendarEvent",
                            "PKDataDetectorTypeLink",
                            "PKDataDetectorTypePhoneNumber",
                            "PKDataDetectorTypeAddress"
                        ]
                    ],
                    [
                        "changeMessage" => "universal.optIn.cm",
                        "key" => "universal.optIn",
                        "label" => "universal.optIn.label",
                        "value" => "universal.optIn.value",
                        "attributedValue" => "universal.optIn.value",
                        "dataDetectorTypes" => [
                            "PKDataDetectorTypeAddress",
                            "PKDataDetectorTypeCalendarEvent",
                            "PKDataDetectorTypeLink",
                            "PKDataDetectorTypePhoneNumber"
                        ]
                    ],
                    [
                        "key" => "pktest",
                        "label" => "legal.label",
                        "value" => "legal.value",
                        "attributedValue" => "legal.value"
                    ]
                ],
                "headerFields" => [
                    [
                        "changeMessage" => "members.member.points.cm",
                        "key" => "members.member.points",
                        "label" => "members.member.points.label",
                        "value" => 104,
                        "textAlignment" => "PKTextAlignmentRight"
                    ]
                ],
                "primaryFields" => [
                    [
                        "key" => "person.mobileNumber",
                        "label" => "person.mobileNumber.label",
                        "value" => "person.mobileNumber.value",
                        "textAlignment" => "PKTextAlignmentLeft"
                    ]
                ],
                "secondaryFields" => [
                    [
                        "key" => "person.displayName",
                        "label" => "person.displayName.label",
                        "value" => "person.displayName.value",
                        "textAlignment" => "PKTextAlignmentLeft"
                    ],
                    [
                        "key" => "members.tier.name",
                        "label" => "members.tier.name.label",
                        "value" => "members.tier.name.value",
                        "textAlignment" => "PKTextAlignmentRight"
                    ]
                ]
            ],
            "locations" => [
                [
                    "latitude" => 53.92102,
                    "longitude" => 27.579749,
                    "relevantText" => "кульман"
                ]
            ],
            "barcode" => [
                "altText" => "4Hg9MPyhErG9XHLyo1EM1q",
                "format" => "PKBarcodeFormatQR",
                "message" => "4Hg9MPyhErG9XHLyo1EM1q",
                "messageEncoding" => "iso-8859-1"
            ],
            "barcodes" => [
                [
                    "altText" => "4Hg9MPyhErG9XHLyo1EM1q",
                    "format" => "PKBarcodeFormatQR",
                    "message" => "4Hg9MPyhErG9XHLyo1EM1q",
                    "messageEncoding" => "iso-8859-1"
                ]
            ],
            "backgroundColor" => "rgb(210,105,111)",
            "foregroundColor" => "rgb(255,255,255)",
            "labelColor" => "rgb(255,255,255)",
            "authenticationToken" => "5JbPXzVLTQykM36bcO8QOC",
            "webServiceURL" => route('passd.root'),
            "sharing" => [
                "method" => "PKPassSharingMethodURL",
                "url" => "https://example.com/share"
            ]
        ];

        $pass->setData($jayParsedAry);

        // Add files to the pass package
        $pass->addFile(storage_path('demo/pass/icon.png'));
        $pass->addFile(storage_path('demo/pass/<EMAIL>'));
        $pass->addFile(storage_path('demo/pass/<EMAIL>'));
        $pass->addFile(storage_path('demo/pass/logo.png'));
        $pass->addFile(storage_path('demo/pass/<EMAIL>'));
        $pass->addFile(storage_path('demo/pass/<EMAIL>'));
        $pass->addFile(storage_path('demo/pass/background.png'));

        // Create and output the pass
        $file = $pass->create();
    });

    file_put_contents(public_path("{$certificate->pass_type_id}.pkpass"), $file);
});



Artisan::command('push', function () {

    $pass = Pass::first();
    $project = $pass->template->project;
    $certificate = $project->certificate;

    $pass->touch();

//    $push_service = new ApnsPHP_Push(
//        ApnsPHP_Abstract::ENVIRONMENT_PRODUCTION,
//        config('apns.push_certificate')
//    );
//
//    $push_service->setRootCertificationAuthority(config('apns.root_ca_certificate'));
//
//    $push_service->connect();
//
//    $message = new ApnsPHP_Message($token);
//    $push_service->add($message);
//    $push_service->send();
//
//    $push_service->disconnect();
//
//    $aErrorQueue = $push_service->getErrors();
//    if (!empty($aErrorQueue)) {
//        var_dump($aErrorQueue);
//    }

});


Artisan::command('cert', function () {

    dd(
        \App\Services\Apple\CertificateService::getPublicKey(storage_path('certificates/private.key'), 'pkey'),
        \App\Services\Apple\CertificateService::getPublicKey(storage_path('demo/app.csr'), 'req'),
        \App\Services\Apple\CertificateService::getPublicKey(storage_path('demo/pass.cer'), 'x509'),
        \App\Services\Apple\CertificateService::getX509Subject(storage_path('demo/pass.cer')),
    );

});
