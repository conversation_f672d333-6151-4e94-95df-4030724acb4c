<?php

use App\Http\Controllers\Passd\LogController;
use App\Http\Controllers\Passd\PassController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Passd\RegistrationController;

Route::get('/', null)->name('root');

Route::group(['prefix' => 'v1'], function () {
    Route::group(['prefix' => 'devices/{device_uid}'], function () {
        Route::group(['prefix' => 'registrations/{pass_type_id}'], function () {
            Route::get('/', [RegistrationController::class, 'index']);
            Route::post('/{serial_number}', [RegistrationController::class, 'store'])->middleware('auth.apple-pass');
            Route::delete('/{serial_number}', [RegistrationController::class, 'delete'])->middleware('auth.apple-pass');
        });
    });

    Route::get('passes/{pass_type_id}/{serial_number}', [PassController::class, 'show'])->middleware('auth.apple-pass')->name('passes.index');
    Route::post('log', LogController::class);
});
