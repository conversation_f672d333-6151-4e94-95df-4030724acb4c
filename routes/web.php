<?php

use App\Http\Controllers\PassController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\TemplateController;
use App\Http\Controllers\UtilsController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return redirect()->route('projects.index');
});

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified'
])->group(function () {
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');

    Route::resource('projects', ProjectController::class)->except('edit');
    Route::resource('locations', TemplateController::class);
    Route::resource('beacons', TemplateController::class);

    Route::get('projects/create', \App\Http\Livewire\Projects\Edit::class)->name('projects.create');
    Route::get('projects/{project}/edit', \App\Http\Livewire\Projects\Edit::class)->name('projects.edit');

    Route::get('projects/{project}/passes', \App\Http\Livewire\Passes\Index::class)->name('projects.passes.index');
    Route::get('projects/{project}/passes/create', \App\Http\Livewire\Passes\Edit::class)->name('projects.passes.create');
    Route::get('passes/{pass}/edit', \App\Http\Livewire\Passes\Edit::class)->name('passes.edit');

    Route::get('fields/{field}/edit', \App\Http\Livewire\Fields\Edit::class)->name('fields.edit');
    Route::get('projects/{project}/fields/create', \App\Http\Livewire\Fields\Edit::class)->name('fields.create');

    Route::get('templates/{template}/edit', \App\Http\Livewire\Templates\Edit::class)->name('templates.edit');
    Route::get('projects/{project}/templates/create', \App\Http\Livewire\Templates\Edit::class)->name('templates.create');

    Route::resource('projects.templates', TemplateController::class)->shallow()->except('edit', 'create');
    Route::resource('projects.fields', TemplateController::class)->shallow()->except('edit', 'create');
});

Route::get('utils/qr', [UtilsController::class, 'qr'])->name('utils.qr');
