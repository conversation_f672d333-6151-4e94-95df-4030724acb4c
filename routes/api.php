<?php

use App\Http\Controllers\Api\BeaconController;
use App\Http\Controllers\Api\CertificateController;
use App\Http\Controllers\Api\FieldController;
use App\Http\Controllers\Api\LocationController;
use App\Http\Controllers\Api\PassController;
use App\Http\Controllers\Api\ProjectController;
use App\Http\Controllers\Api\TemplateController;
use App\Http\Controllers\Api\Utils\CsrController;
use App\Http\Controllers\Api\Utils\EnumController;
use App\Http\Controllers\Api\Utils\StringController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->group(function () {

    Route::apiResource('certificates', CertificateController::class)->except('update');
    Route::apiResource('projects', ProjectController::class);
    Route::apiResource('projects.templates', TemplateController::class)->shallow();
    Route::apiResource('projects.templates.passes', PassController::class)->shallow();
    Route::apiResource('projects.fields', FieldController::class)->shallow();
    Route::apiResource('locations', LocationController::class);
    Route::apiResource('beacons', BeaconController::class);

    Route::post('passes/{pass}/send-push', [PassController::class, 'sendPush']);

    Route::group(['prefix' => 'utils'], function () {
        Route::get('csr', CsrController::class);
        Route::get('enums', EnumController::class);
        Route::get('strings', StringController::class);
    });

});
