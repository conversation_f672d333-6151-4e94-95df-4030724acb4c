<?php

namespace App\Jobs\Passd;

use App\Models\Certificate;
use App\Models\Pass;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class SendPush implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected Certificate $certificate;
    protected Collection $pushTokens;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Pass $pass)
    {
        $this->pushTokens = $pass->registrations()->pluck('push_token')->unique();
        $this->certificate = $pass->template->project->certificate;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->certificate->withFiles(function ($pemPath, $p12Path) {
            foreach ($this->pushTokens as $pushToken) {
                $url = "https://api.push.apple.com/3/device/{$pushToken}";

                (new \GuzzleHttp\Client())->post($url, [
                    'headers' => ['apns-topic' => $this->certificate->pass_type_id],
                    'json' => json_decode('{}'),
                    'version' => 2.0,
                    'cert' => [$p12Path, '']
                ]);
            }
        });
    }
}
