<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class GeneratePrivate<PERSON>ey extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'key:generate-private {--force}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $privateKeyPath = storage_path('certificates/private.key');

        $force = $this->option('force');
        $quiet = $this->option('quiet');

        if (file_exists($privateKeyPath) && ! $force) {
            if ($quiet) {
                return Command::SUCCESS;
            }

            if (! $this->confirm('Do you wish to overwrite private key?')) {
                return Command::SUCCESS;
            }
        }

        exec("openssl genrsa -out {$privateKeyPath} 2048 2>&1");

        $this->info('New private key has been generated.');

        return Command::SUCCESS;
    }
}
