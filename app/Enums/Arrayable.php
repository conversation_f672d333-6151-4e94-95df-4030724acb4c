<?php

namespace App\Enums;

use Illuminate\Validation\Rules\In;

trait Arrayable
{
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function keys(): array
    {
        return array_column(self::cases(), 'name');
    }

    public static function pairs(): array
    {
        return array_combine(self::keys(), self::values());
    }

    public static function validationRuleIn(): In
    {
        return new In(self::values());
    }
}
