<?php

namespace App\Enums;

use App\Models\Field;
use Illuminate\Validation\Rules\In;

enum FieldTypeEnum: string
{
    use Arrayable;

    case STRING = 'string';
    case INTEGER = 'integer';
    case NUMERIC = 'numeric';
    case BOOLEAN = 'boolean';
    case ENUM = 'enum';

    public function getValidationRules(Field $field)
    {
        $rules = [];

        if ($this != self::ENUM) {
            $rules[] = $this->value;
        }

        if ($this == self::ENUM) {
            $rules[] = new In($field->enum);
        }

        return $rules;
    }
}
