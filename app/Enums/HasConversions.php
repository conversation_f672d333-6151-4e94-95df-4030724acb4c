<?php

namespace App\Enums;

use Spatie\Image\Manipulations;
use Spatie\MediaLibrary\HasMedia;

trait HasConversions
{
    abstract private function getConversions(): array;

    public function registerMediaConversions(HasMedia $mediable): void
    {
        $conversions = $this->getConversions();

        foreach ($conversions as $conversionName => $dimensions) {
            if ($conversionName != 'original') {
                $mediable
                    ->addMediaConversion($conversionName)
                    ->performOnCollections($this->value)
                    ->fit(Manipulations::FIT_CROP, ...$dimensions)
                    ->format('png')
                    ->queued();
            }
        }
    }
}
