<?php

namespace App\Enums\Apple;

use App\Enums\Arrayable;
use App\Enums\HasConversions;
use Spatie\Image\Manipulations;
use <PERSON>tie\MediaLibrary\HasMedia;

enum ImageTypeEnum: string
{
    use Arrayable;
    use HasConversions;

    case ICON = 'icon';
    case LOGO = 'logo';
    case LOGO_SQUARE = 'logo__square';
    case STORE_CARD_STRIP = 'strip__store-card';

    public function getDimensions($multiplier = 1): array
    {
        $dimensions = match ($this) {
            self::ICON => [114, 114],
            self::LOGO => [480, 150],
            self::LOGO_SQUARE => [150, 150],
            self::STORE_CARD_STRIP => [1125, 432],
        };

        return array_map(fn ($x) => $x * $multiplier, $dimensions);
    }

    public function getRootValue(): string
    {
        return explode('__', $this->value)[0];
    }

    protected function getConversions(): array
    {
        return [
            '1x' => $this->getDimensions(1/3),
            '2x' => $this->getDimensions(2/3),
            '3x' => $this->getDimensions(),
        ];
    }
}
