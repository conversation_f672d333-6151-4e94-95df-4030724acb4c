<?php

namespace App\Enums\Apple;

use App\Enums\Arrayable;

enum PassStyleEnum: string
{
    use Arrayable;

    case STORE_CARD = 'storeCard';
//    case BOARDING_PASS = 'boardingPass';
//    case COUPON = 'coupon';
//    case EVENT_TICKET = 'eventTicket';
//    case GENERIC = 'generic';

    public function getSupportedImageTypes(): array
    {
        return match ($this) {
            self::STORE_CARD => [
                ImageTypeEnum::ICON,
                ImageTypeEnum::LOGO,
                ImageTypeEnum::LOGO_SQUARE,
                ImageTypeEnum::STORE_CARD_STRIP,
            ],
        };
    }
}
