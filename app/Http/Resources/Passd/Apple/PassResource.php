<?php

namespace App\Http\Resources\Passd\Apple;

use App\Models\Beacon;
use App\Models\Location;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Routing\UrlGenerator;
use Illuminate\Support\Collection;

class PassResource extends JsonResource
{
    const FORMAT_VERSION = 1;

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        app(UrlGenerator::class)->forceScheme('https');
        app(UrlGenerator::class)->forceRootUrl(config('app.public_url'));

        $output = [
            "formatVersion" => self::FORMAT_VERSION,
            "description" => $this->template->description,
            "organizationName" => $this->template->organization_name,
            "passTypeIdentifier" => $this->template->project->certificate->pass_type_id,
            "serialNumber" => $this->id,
            "teamIdentifier" => $this->template->project->certificate->team_id,
            "authenticationToken" => $this->authorization_token,
            "webServiceURL" => route('passd.root'),
            "voided" => (int) isset($this->deleted_at),
            "expirationDate" => $this->whenNotNull($this->deleted_at ?? $this->expires_at),
            "maxDistance" => $this->whenNotNull($this->template->max_distance),
            "associatedStoreIdentifiers" => $this->template->associated_apps['ios'] ?? [],
            ...$this->getFields(),
            ...$this->getBarcodes(),
            ...$this->getLocations(),
            ...$this->getBeacons(),
            ...$this->getSharing(),
            ...$this->getColors(),
        ];
        app(UrlGenerator::class)->forceScheme(null);
        app(UrlGenerator::class)->forceRootUrl(null);

        return $output;
    }

    protected function getFields()
    {
        $style = $this->resource->template->style;

        $fields = collect($this->template->fields)
            ->map(function ($fields) {
                return collect($fields)->map(function ($field) {
                    return $this->wrappedResource(
                        class: FieldResource::class,
                        resource: (object) $field,
                    );
                });
            });

        return [$style->value => $fields];
    }

    protected function getBarcodes()
    {
        $barcodeResource = $this->wrappedResource(
            class: BarcodeResource::class,
            resource: $this->template->barcode
        );

        return [
            "barcode" => $barcodeResource,
            "barcodes" => [$barcodeResource],
        ];
    }

    protected function getLocations()
    {
        $templateId = $this->template->id;
        $projectId = $this->template->project_id;

        $locations = Location::query()
            ->whereHas('passes', fn ($query) => $query->where('passes.id', $this->id))
            ->orWhereHas('templates', fn ($query) => $query->where('templates.id', $templateId))
            ->orWhereHas('projects', fn ($query) => $query->where('projects.id', $projectId));

        return [
            "locations" => $this->wrappedCollection(
                class: LocationResource::class,
                collection: $locations->get(),
            ),
        ];
    }

    protected function getBeacons()
    {
        $templateId = $this->template->id;
        $projectId = $this->template->project_id;

        $beacons = Beacon::query()
            ->whereHas('passes', fn ($query) => $query->where('passes.id', $this->id))
            ->orWhereHas('templates', fn ($query) => $query->where('templates.id', $templateId))
            ->orWhereHas('projects', fn ($query) => $query->where('projects.id', $projectId));

        return [
            "beacons" => $this->wrappedCollection(
                class: BeaconResource::class,
                collection: $beacons->get(),
            ),
        ];
    }

    protected function wrappedCollection($class, Collection $collection)
    {
        return $collection->map(function ($resource) use ($class) {

            return $this->wrappedResource(
                class: $class,
                resource: $resource
            );
        });
    }

    protected function getSharing()
    {
        return [
            "sharing" => $this->wrappedResource(
                class: SharingResource::class,
                resource: $this->template->sharing,
            )
        ];
    }

    protected function getColors()
    {
        return [
            'backgroundColor' => $this->whenNotNull($this->template->colors['background'] ?? null),
            'foregroundColor' => $this->whenNotNull($this->template->colors['foreground'] ?? null),
            'labelColor' => $this->whenNotNull($this->template->colors['label'] ?? null),
        ];
    }

    protected function wrappedResource($class, $resource)
    {
        return (new $class($resource))->additional([
            'pass' => $this->resource,
        ]);
    }
}
