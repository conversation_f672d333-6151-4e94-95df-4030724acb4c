<?php

namespace App\Http\Resources\Passd\Apple;

use App\Http\Resources\Passd\PassFullResource;

class BeaconResource extends PassFullResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'proximityUUID' => $this->proximity_uuid,
            'major' => $this->whenNotNull($this->major),
            'minor' => $this->whenNotNull($this->minor),
            'relevantText' => $this->whenNotNull($this->eval($this->relevant_text)),
        ];
    }
}
