<?php

namespace App\Http\Resources\Passd\Apple;

use App\Http\Resources\Passd\PassFullResource;

class BarcodeResource extends PassFullResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'altText' => $this->whenNotNull($this->eval($this->resource['alt_text'] ?? null)),
            'format' => $this->resource['format'],
            'message' => $this->eval($this->resource['message']),
            'messageEncoding' => $this->resource['message_encoding'],
        ];
    }
}
