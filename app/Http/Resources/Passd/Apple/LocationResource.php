<?php

namespace App\Http\Resources\Passd\Apple;

use App\Http\Resources\Passd\PassFullResource;

class LocationResource extends PassFullResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'altitude' => $this->whenNotNull($this->altitude),
            'relevantText' => $this->whenNotNull($this->eval($this->relevant_text)),
        ];
    }

    protected function eval($placeholder)
    {
        $placeholder = $this->evaluatePlaceholder($placeholder);

        return parent::eval($placeholder);
    }
}
