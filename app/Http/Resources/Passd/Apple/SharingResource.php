<?php

namespace App\Http\Resources\Passd\Apple;

use App\Http\Resources\Passd\PassFullResource;

class SharingResource extends PassFullResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'method' => $this->resource['method'],
            'url' => $this->eval($this->resource['url']),
        ];
    }
}
