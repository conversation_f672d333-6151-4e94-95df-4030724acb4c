<?php

namespace App\Http\Resources\Passd\Apple;

use App\Http\Resources\Passd\PassFullResource;

class FieldResource extends PassFullResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'key' => $this->key,
            'value' => $this->getValue(),
            'attributedValue' => $this->whenNotNull($this->eval($this->attributed_value ?? null)),
            'changeMessage' => $this->whenNotNull($this->eval($this->change_message ?? null)),
            'dataDetectorTypes' => $this->whenNotNull($this->data_detector_types ?? null),
            'label' => $this->whenNotNull($this->eval($this->label ?? null)),
            'textAlignment' => $this->whenNotNull($this->text_alignment ?? null),
            'dateStyle' => $this->whenNotNull($this->date_style ?? null),
            'numberStyle' => $this->whenNotNull($this->number_style ?? null),
        ];
    }

    protected function getValue()
    {
        $value = $this->value;

        if (is_null($value)) {
            $value = sprintf('${%s.value}', $this->key);
        }

        $value = $this->eval($value);

        if (isset($this->number_style)) {
            if (!is_numeric($value)) {
                $value = 0;
            }

            $value = $value + 0;
        }

        return $value;
    }
}
