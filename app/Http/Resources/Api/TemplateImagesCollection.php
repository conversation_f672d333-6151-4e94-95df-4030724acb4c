<?php

namespace App\Http\Resources\Api;

use App\Enums\Apple\ImageTypeEnum;
use Illuminate\Http\Resources\Json\ResourceCollection;

class TemplateImagesCollection extends ResourceCollection
{
    public $collects = MediaResource::class;

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return $this->collection
            ->map([$this, 'setDimensions'])
            ->keyBy('collection_name')
            ->map->toArray($request)
            ->all();
    }

    public function setDimensions($medium)
    {
        $medium->dimensions = ImageTypeEnum::from(
            $medium->resource->collection_name
        )->getDimensions();
        return $medium;
    }
}
