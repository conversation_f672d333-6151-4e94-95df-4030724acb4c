<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Routing\UrlGenerator;

class PassResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'external_id' => $this->external_id,
            'authorization_token' => $this->authorization_token,
            'locations' => LocationResource::collection($this->whenLoaded('locations')),
            'beacons' => LocationResource::collection($this->whenLoaded('beacons')),
            'fields' => FieldResource::collection($this->whenLoaded('fields')),
            'install_url' => $this->install_url,
        ];
    }
}
