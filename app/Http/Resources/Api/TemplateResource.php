<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class TemplateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $minimum = [
            'id' => $this->id,
            'title' => $this->title,
            'target' => $this->target,
            'style' => $this->style,
            'description' => $this->description,
            'organization_name' => $this->organization_name,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];

        if ($request->route()->getName() == 'templates.index') {
            return $minimum;
        }

        return array_merge($minimum, [
            'project' => $this->whenLoaded('project'),
            'images' => new TemplateImagesCollection($this->whenLoaded('media')),
            'colors' => $this->colors,
            'barcode' => $this->barcode,
            'sharing' => $this->sharing,
            'associated_apps' => $this->associated_apps,
            'fields' => $this->fields,
            'locations' => LocationResource::collection($this->whenLoaded('locations')),
            'beacons' => BeaconResource::collection($this->whenLoaded('beacons')),
        ]);
    }
}
