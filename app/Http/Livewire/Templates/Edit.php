<?php

namespace App\Http\Livewire\Templates;

use App\Models\Project;
use App\Models\Template;
use Livewire\Component;

class Edit extends Component
{
    public Project $project;
    public Template $template;

    protected function getRules()
    {
        return [
            'template.title' => [],
            'template.organization_name' => [],
            'template.description' => [],
            'template.colors.background' => [],
            'template.colors.foreground' => [],
            'template.colors.label' => [],
            'template.barcode.alt_text' => [],
        ];
    }

    public function updated()
    {
        $this->emitSelf('refreshComponent');
    }

    public function save()
    {
        $this->validate();

        $this->template->save();

        $this->emit('saved');
    }

    public function mount()
    {
        if ($this->template->id) {
            $this->project = $this->template->project;
        }

        if ($this->project->id) {
            $this->template->project_id = $this->project->id;
        }
    }

    public function render()
    {
        return view('livewire.templates.edit');
    }
}
