<?php

namespace App\Http\Livewire\Fields;

use Livewire\Component;

class EnumInput extends Component
{
    public string $newValue = '';

    public ?array $enum = [];

    protected $rules = [
        'newValue' => ['required'],
    ];

    public function add()
    {
        $this->validate();

        $this->enum[] = $this->newValue;

        $this->reset('newValue');

        $this->enumUpdated();
    }

    public function remove($index)
    {
        unset($this->enum[$index]);

        $this->enumUpdated();
    }

    public function enumUpdated()
    {
        $this->emitUp('enumUpdated', $this->enum);
    }

    public function mount($enum)
    {
        $this->enum = $enum ?? [];
    }

    public function render()
    {
        return view('livewire.fields.enum-input');
    }
}
