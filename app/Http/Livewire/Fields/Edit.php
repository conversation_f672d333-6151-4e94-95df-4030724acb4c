<?php

namespace App\Http\Livewire\Fields;

use App\Enums\FieldTypeEnum;
use App\Models\Field;
use App\Models\Project;
use Illuminate\Validation\Rules\Enum;
use Livewire\Component;

class Edit extends Component
{
    public Project $project;
    public Field $field;

    protected $listeners = [
        'enumUpdated' => 'enumUpdated',
    ];

    protected function getRules()
    {
        return [
            'field.id' => [],
            'field.title' => ['required', 'string', 'max:255'],
            'field.key' => ['required', 'string', 'max:255'],
            'field.type' => ['required', new Enum(FieldTypeEnum::class)],
            'field.required' => ['required', 'boolean'],
            'field.project_id' => ['required', 'exists:projects,id'],
            'field.enum' => ['required_if:field.type,'.FieldTypeEnum::ENUM->value, 'array', 'nullable'],
            'field.default_value' => [],
            'field.show_in_list' => ['required', 'boolean'],
        ];
    }

    public function save()
    {
        $this->validate();

        $this->field->save();

        return redirect()->route('fields.edit', $this->field->id);
    }

    public function enumUpdated($enum)
    {
        $this->field->enum = $enum;
        $this->resetValidation('field.enum');
    }

    public function mount(Project $project, Field $field)
    {
        if ($project->id) {
            $this->project = $project;
        } else {
            $this->project = $this->field->project;
        }

        $this->field = $field;
        $this->field->project_id = $this->project->id;
        $this->field->required = $this->field->required ?? false;
        $this->field->type = $this->field->type ?? FieldTypeEnum::STRING;
    }

    public function render()
    {
        return view('livewire.fields.edit');
    }
}
