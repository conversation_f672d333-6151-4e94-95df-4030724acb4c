<?php

namespace App\Http\Livewire\Passes;

use App\Actions\Api\UpdateFieldsInPass;
use App\Actions\Api\UpdateOrCreatePass;
use App\Models\Pass;
use App\Models\Project;
use Livewire\Component;

class Edit extends Component
{
    public $project;

    public $pass;

    public $fields;

    protected $rules = [
        'pass.id' => [],
        'pass.template_id' => ['required', 'exists:templates,id'],
        'pass.external_id' => [],
    ];

    public function save()
    {
        $fields = collect($this->fields)->mapWithKeys(function ($field) {
            return [$field['key'] => $field['value'] ?? null];
        })->toArray();

        $this->pass->save();
        $this->pass->touch();

        UpdateFieldsInPass::run(
            pass: $this->pass,
            fields: $fields,
            patch: true,
        );

        $this->emit('saved');
    }

    public function mount(Project $project, Pass $pass)
    {
        $this->pass = $pass;
        $this->project = $project->id ? $project : $this->pass->template->project;
        $this->pass->project_id = $this->project->id;

        $this->fields = $this->project->fields()->orderByDesc('required')->with('passes', function ($query) {
            $query->where('passes.id', $this->pass->id);
        })->get();

        $this->fields = $this->fields->map(function ($field) {
            $field->value = $field->passes->first()?->pivot->value ?? null;
            unset($field->passes);

            return $field;
        })->toArray();
    }

    public function render()
    {
        return view('livewire.passes.edit');
    }
}
