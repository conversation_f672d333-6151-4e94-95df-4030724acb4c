<?php

namespace App\Http\Livewire\Projects;

use App\Enums\ProjectTypeEnum;
use App\Http\Controllers\Api\Utils\CsrController;
use App\Models\Project;
use App\Services\Apple\CertificateService;
use Illuminate\Validation\Rules\Enum;
use JetBrains\PhpStorm\ArrayShape;
use Livewire\Component;
use Livewire\WithFileUploads;

class Edit extends Component
{
    use WithFileUploads;

    public Project $project;

    public $uploadedCertificate;

    public $certificate;

    protected function getRules()
    {
        return [
            'project.title' => ['required', 'string'],
            'project.type' => ['required', new Enum(ProjectTypeEnum::class)],
            'project.user_id' => ['required', 'exists:users,id'],
            'project.external_id_field_id' => ['nullable', 'exists:fields,id'],
            'uploadedCertificate' => ['nullable', 'file'],
        ];
    }

    public function saveProject()
    {
        $this->validate();

        $this->project->save();

        $this->emit('projectSaved');

        if ($this->project->wasRecentlyCreated) {
            return redirect()->route('projects.edit', $this->project->id);
        }
    }

    public function saveCertificate()
    {
        $this->resetErrorBag();

        $derContent = $this->uploadedCertificate->get();
        $certificateService = (new CertificateService($derContent));

        if ($certificateService->check()) {
            $certificate = auth()->user()->certificates()->create($certificateService->getAttributes());
            $this->project->certificate()->associate($certificate);
            $this->project->save();
            $this->uploadedCertificate = null;
            $this->emit('certificateSaved');
        } else {
            $this->addError('uploadedCertificate', 'Invalid certificate uploaded');
        }

    }

    public function downloadCsr()
    {
        return (new CsrController)();
    }

    public function mount(Project $project)
    {
        $this->project = $project;
        $this->project->user_id = $this->project->user_id ?? auth()->user()->id;
        $this->project->type = $this->project->type ?? ProjectTypeEnum::LOYALTY;
    }

    public function render()
    {
        return view('livewire.projects.edit');
    }
}
