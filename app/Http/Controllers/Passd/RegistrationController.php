<?php

namespace App\Http\Controllers\Passd;

use App\Enums\PassEventTypeEnum;
use App\Http\Controllers\Controller;
use App\Models\Certificate;
use App\Models\Device;
use App\Models\Pass;
use App\Models\PassEvent;
use App\Models\Registration;
use Illuminate\Http\Request;

class RegistrationController extends Controller
{
    public function index(Request $request, $deviceUid, $passTypeId)
    {
        $device = Device::firstOrCreate(['uid' => $deviceUid]);

        $passes = Pass::withTrashed()
            ->where('updated_at', '>', $device->updated_at)
            ->whereHas('registrations', function ($query) use ($device) {
                $query->whereDeviceId($device->id);
            });

        $device->touch();

        if ($passes->count()) {
            return [
                'lastUpdated' => now(),
                'serialNumbers' => $passes->pluck('id')
            ];
        }

        return response()->noContent();
    }

    public function store(Request $request, $deviceUid, $passTypeId, $serialNumber)
    {
        $device = Device::firstOrCreate(['uid' => $deviceUid]);

        $pass = Pass::withTrashed()->findOrFail($serialNumber);

        $registration = Registration::firstOrCreate([
            'device_id' => $device->id,
            'pass_id' => $pass->id,
        ], [
            'device_id' => $device->id,
            'pass_id' => $pass->id,
            'push_token' => $request->pushToken
        ]);

        $device->touch();

        if ($registration->wasRecentlyCreated) {

            PassEvent::create([
                'pass_id' => $pass->id,
                'project_id' => $pass->template->project_id,
                'type' => PassEventTypeEnum::INSTALL,
            ]);

            return response()->noContent(201);
        }

        return response()->noContent(304);
    }

    public function delete(Request $request, $deviceUid, $passTypeId)
    {
        $registration = Registration::query()
            ->whereHas('device', function ($query) use ($deviceUid) {
                $query->where('uid', $deviceUid);
            })
            ->whereHas('pass.template.project.certificate', function ($query) use ($passTypeId) {
                $query->where('pass_type_id', $passTypeId);
            })->first();


        $pass = $registration->pass;

        $registration->delete();

        PassEvent::create([
            'pass_id' => $pass->id,
            'project_id' => $pass->template->project_id,
            'type' => PassEventTypeEnum::UNINSTALL,
        ]);

        return response()->noContent(200);
    }
}
