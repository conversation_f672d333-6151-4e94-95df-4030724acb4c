<?php

namespace App\Http\Controllers\Passd;

use App\Http\Controllers\Controller;
use App\Models\Pass;
use App\Services\Apple\PassService;
use Carbon\Carbon;
use Illuminate\Http\Request;

class PassController extends Controller
{
    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Pass  $pass
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $pass_type_id, $serial_number)
    {
        $since = null;

        if ($request->hasHeader('if-modified-since')) {
            $since = new Carbon($request->header('if-modified-since'));
        }

        $pass = Pass::withTrashed()
            ->when(! is_null($since), function ($query) use ($since) {
                $query->where('updated_at', '>', $since);
            })
            ->find($serial_number);

        if ($pass) {
            $generator = new PassService($pass);

            $content = $generator->create();

            return response($content)->withHeaders([
                'content-disposition' => 'attachment; filename="pass.pkpass"',
                'content-transfer-encoding' => 'binary',
                'content-type' => 'application/vnd.apple.pkpass',
                'x-passkit-pid' => $pass->id,
                'cache-control' => 'no-store, no-cache, must-revalidate, post-check=0, pre-check=0',
                'last-modified' => date("D, d M Y H:i:s", time()) . ' GMT'
            ]);
        }

        return response()->noContent(304);
    }
}
