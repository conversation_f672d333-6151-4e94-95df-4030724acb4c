<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\LocationCreateRequest;
use App\Http\Requests\Api\LocationUpdateRequest;
use App\Http\Resources\Api\LocationResource;
use App\Models\Location;
use Illuminate\Http\Request;

class LocationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function index(Request $request)
    {
        $query = $request->user()->locations();

        return LocationResource::collection(
            $query->paginate()
        );
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return LocationResource
     */
    public function store(LocationCreateRequest $request)
    {
        $location = null;

        if ($request->has('external_id')) {
            $location = $request->user()->locations()->firstWhere([
                'external_id' => $request->external_id,
            ]);
        }

        if (isset($location)) {
            $location->update($request->validated());
        } else {
            $location = $request->user()->locations()->create(
                $request->validated()
            );
        }

        return $this->show($location);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Location  $location
     * @return LocationResource
     */
    public function show(Location $location)
    {
        return new LocationResource($location);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Location  $location
     * @return LocationResource
     */
    public function update(LocationUpdateRequest $request, Location $location)
    {
        $location->update(
            $request->validated()
        );

        return $this->show($location);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Location  $location
     * @return \Illuminate\Http\Response
     */
    public function destroy(Location $location)
    {
        $location->delete();

        return response()->noContent();
    }
}
