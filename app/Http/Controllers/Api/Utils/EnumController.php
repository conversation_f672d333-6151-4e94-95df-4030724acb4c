<?php

namespace App\Http\Controllers\Api\Utils;

use App\Enums\Apple\BarcodeFormatEnum;
use App\Enums\Apple\BarcodeMessageEncodingEnum;
use App\Enums\Apple\CurrencyCodeEnum;
use App\Enums\Apple\DataDetectorTypeEnum;
use App\Enums\Apple\DateStyleEnum;
use App\Enums\Apple\FieldSectionEnum;
use App\Enums\Apple\ImageTypeEnum;
use App\Enums\Apple\NumberStyleEnum;
use App\Enums\Apple\PassStyleEnum;
use App\Enums\Apple\SharingMethodEnum;
use App\Enums\Apple\TextAlignmentEnum;
use App\Enums\FieldTypeEnum;
use App\Enums\TargetEnum;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class EnumController extends Controller
{
    const ENUMS = [
        BarcodeFormatEnum::class,
        BarcodeMessageEncodingEnum::class,
        CurrencyCodeEnum::class,
        DataDetectorTypeEnum::class,
        DateStyleEnum::class,
        FieldSectionEnum::class,
        ImageTypeEnum::class,
        NumberStyleEnum::class,
        PassStyleEnum::class,
        SharingMethodEnum::class,
        TextAlignmentEnum::class,

        FieldTypeEnum::class,
        TargetEnum::class,
    ];

    /**
     * Handle the incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function __invoke(Request $request)
    {
        return collect(self::ENUMS)->mapWithKeys(function ($className) {
            return [
                $this->getKey($className) => $this->getValues($className)
            ];
        });
    }

    protected function getKey($className)
    {
        $key = class_basename($className);

        return Str::of($key)->snake()->remove('_enum')->toString();
    }

    protected function getValues($className)
    {
        return collect($className::pairs())->mapWithKeys(function ($value, $key) {

            $key = Str::of($key)->lower()->toString();

            return [$key => $value];
        });
    }
}
