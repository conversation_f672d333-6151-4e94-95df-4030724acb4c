<?php

namespace App\Http\Controllers\Api\Utils;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class StringController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\Translation\Translator|string
     */
    public function __invoke(Request $request)
    {
        return __('frontend');
    }
}
