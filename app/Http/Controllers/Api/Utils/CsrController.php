<?php

namespace App\Http\Controllers\Api\Utils;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\CsrGetRequest;
use App\Services\Apple\CertificateService;

class CsrController extends Controller
{
    public function __invoke()
    {
        $user = auth()->user();

        return response()->streamDownload(function () use ($user) {
            echo CertificateService::createCsr(
                email: $user->email,
                commonName: $user->name,
            );
        }, 'CertificateSigningRequest.certSigningRequest', [
            'Content-Type' => 'application/pkcs10',
        ]);
    }
}
