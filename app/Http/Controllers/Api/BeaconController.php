<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\BeaconCreateRequest;
use App\Http\Requests\Api\BeaconUpdateRequest;
use App\Http\Resources\Api\BeaconResource;
use App\Models\Beacon;
use Illuminate\Http\Request;

class BeaconController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function index(Request $request)
    {
        $query = $request->user()->beacons();

        return BeaconResource::collection(
            $query->paginate()
        );
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return BeaconResource
     */
    public function store(BeaconCreateRequest $request)
    {
        $beacon = $request->user()->beacons()->create(
            $request->validated()
        );

        return $this->show($beacon);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Beacon  $beacon
     * @return BeaconResource
     */
    public function show(Beacon $beacon)
    {
        return new BeaconResource($beacon);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Beacon  $beacon
     * @return BeaconResource
     */
    public function update(BeaconUpdateRequest $request, Beacon $beacon)
    {
        $beacon->update(
            $request->validated()
        );

        return $this->show($beacon);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Beacon  $beacon
     * @return \Illuminate\Http\Response
     */
    public function destroy(Beacon $beacon)
    {
        $beacon->delete();

        return response()->noContent();
    }
}
