<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\FieldCreateRequest;
use App\Http\Requests\Api\FieldUpdateRequest;
use App\Http\Resources\Api\FieldResource;
use App\Models\Field;
use App\Models\Project;
use Illuminate\Http\Request;

class FieldController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function index(Project $project)
    {
        $query = $project->fields();

        return FieldResource::collection(
            $query->paginate()
        );
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(FieldCreateRequest $request, Project $project)
    {
        $field = $project->fields()->create(
            $request->validated()
        );

        return $this->show($field);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Field  $field
     * @return FieldResource
     */
    public function show(Field $field)
    {
        return new FieldResource($field);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Field  $field
     * @return \Illuminate\Http\Response
     */
    public function update(FieldUpdateRequest $request, Field $field)
    {
        $field->update(
            $request->validated()
        );

        return $this->show($field);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Field  $field
     * @return \Illuminate\Http\Response
     */
    public function destroy(Field $field)
    {
        $field->delete();

        return response()->noContent();
    }
}
