<?php

namespace App\Http\Controllers\Api;

use App\Actions\Api\UpdateOrCreatePass;
use App\Actions\Api\SyncBeaconsInBeaconable;
use App\Actions\Api\UpdateFieldsInPass;
use App\Actions\Api\SyncLocationsInLocationable;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\PassCreateRequest;
use App\Http\Requests\Api\PassUpdateRequest;
use App\Http\Resources\Api\PassResource;
use App\Jobs\Passd\SendPush;
use App\Models\Pass;
use App\Models\Project;
use App\Models\Template;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class PassController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function index(Project $project, Template $template)
    {
        $query = $template->passes();

        return PassResource::collection(
            $query->paginate()
        );
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(PassCreateRequest $request, Project $project, Template $template)
    {
        $passData = $request->validated();

        $pass = UpdateOrCreatePass::run(
            project: $project,
            template: $template,
            passData: $passData,
            onDuplicate: $passData['_on_duplicate'] ?? 'reject',
        );

        if (! $pass) {
            abort(304);
        }

        UpdateFieldsInPass::run(
            pass: $pass,
            fields: $passData['fields'] ?? [],
        );

        SyncLocationsInLocationable::run(
            locationable: $pass,
            locationIds: $passData['location_ids'] ?? [],
            locationExternalIds: $passData['location_external_ids'] ?? [],
        );

        SyncBeaconsInBeaconable::run(
            beaconable: $pass,
            beaconIds: $passData['beacon_ids'] ?? [],
        );

        $pass->touch();

        return $this->show($pass);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Pass  $pass
     * @return PassResource
     */
    public function show(Pass $pass)
    {
        $pass->load('fields', 'locations', 'beacons');

        return new PassResource($pass);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Pass  $pass
     * @return PassResource
     */
    public function update(PassUpdateRequest $request, Pass $pass)
    {
        UpdateFieldsInPass::run(
            pass: $pass,
            fields: $request->validated('fields'),
            patch: $request->getMethod() == 'PATCH',
        );

        SyncLocationsInLocationable::run(
            locationable: $pass,
            locationIds: $request->validated('location_ids'),
            locationExternalIds: $request->validated('location_external_ids'),
        );

        SyncBeaconsInBeaconable::run(
            beaconable: $pass,
            beaconIds: $request->validated('beacon_ids'),
        );

        $pass->touch();

        return $this->show($pass);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Pass  $pass
     * @return \Illuminate\Http\Response
     */
    public function destroy(Pass $pass)
    {
        $pass->locations()->detach();
        $pass->beacons()->detach();

        $pass->delete();

        return response()->noContent();
    }

    public function sendPush(Pass $pass)
    {
        $pass->touch();
    }
}
