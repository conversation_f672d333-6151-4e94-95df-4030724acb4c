<?php

namespace App\Http\Controllers\Api;

use App\Actions\Api\SyncBeaconsInBeaconable;
use App\Actions\Api\SyncLocationsInLocationable;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\ProjectCreateRequest;
use App\Http\Requests\Api\ProjectUpdateRequest;
use App\Http\Resources\Api\ProjectResource;
use App\Models\Project;
use Illuminate\Http\Request;

class ProjectController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function index()
    {
        $query = Project::query();

        return ProjectResource::collection(
            $query->paginate()
        );
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return ProjectResource
     */
    public function store(ProjectCreateRequest $request)
    {
        $project = $request->user()->projects()->create(
            $request->validated()
        );

        SyncLocationsInLocationable::run(
            locationable: $project,
            locationIds: $request->validated('location_ids'),
            locationExternalIds: $request->validated('location_external_ids'),
        );

        SyncBeaconsInBeaconable::run(
            beaconable: $project,
            beaconIds: $request->validated('beacon_ids'),
        );

        return $this->show($project);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Project  $project
     * @return ProjectResource
     */
    public function show(Project $project)
    {
        $project->load('certificate');

        return new ProjectResource($project);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Project  $project
     * @return ProjectResource
     */
    public function update(ProjectUpdateRequest $request, Project $project)
    {
        $project->update(
            $request->validated()
        );

        SyncLocationsInLocationable::run(
            locationable: $project,
            locationIds: $request->validated('location_ids'),
            locationExternalIds: $request->validated('location_external_ids'),
        );

        SyncBeaconsInBeaconable::run(
            beaconable: $project,
            beaconIds: $request->validated('beacon_ids'),
        );

        return $this->show($project);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Project  $project
     * @return \Illuminate\Http\Response
     */
    public function destroy(Project $project)
    {
        $project->locations()->detach();
        $project->beacons()->detach();

        $project->delete();

        return response()->noContent();
    }
}
