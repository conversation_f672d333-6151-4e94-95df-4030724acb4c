<?php

namespace App\Http\Controllers\Api;

use App\Actions\Api\AddImagesToTemplate;
use App\Actions\Api\SyncBeaconsInBeaconable;
use App\Actions\Api\SyncLocationsInLocationable;
use App\Enums\Apple\ImageTypeEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\TemplateCreateRequest;
use App\Http\Requests\Api\TemplateUpdateRequest;
use App\Http\Resources\Api\TemplateResource;
use App\Models\Project;
use App\Models\Template;
use Illuminate\Http\Request;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class TemplateController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function index(Project $project)
    {
        $query = $project->templates();

        return TemplateResource::collection(
            $query->paginate()
        );
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return TemplateResource
     */
    public function store(TemplateCreateRequest $request, Project $project)
    {
        $template = $project->templates()->create(
            $request->validated()
        );

        SyncLocationsInLocationable::run(
            locationable: $template,
            locationIds: $request->validated('location_ids'),
            locationExternalIds: $request->validated('location_external_ids'),
        );

        SyncBeaconsInBeaconable::run(
            beaconable: $template,
            beaconIds: $request->validated('beacon_ids'),
        );

        return $this->show($template);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Template  $template
     * @return TemplateResource
     */
    public function show(Template $template)
    {
        $template->load(['media', 'locations', 'beacons']);

        return new TemplateResource($template);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Template  $template
     * @return TemplateResource
     */
    public function update(TemplateUpdateRequest $request, Template $template)
    {
        $template->update(
            $request->validated()
        );

        AddImagesToTemplate::run(
            template: $template,
            images: $request->validated('images'),
        );

        SyncLocationsInLocationable::run(
            locationable: $template,
            locationIds: $request->validated('location_ids'),
            locationExternalIds: $request->validated('location_external_ids'),
        );

        SyncBeaconsInBeaconable::run(
            beaconable: $template,
            beaconIds: $request->validated('beacon_ids'),
        );

        return $this->show($template);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Template  $template
     * @return \Illuminate\Http\Response
     */
    public function destroy(Template $template)
    {
        $template->locations()->detach();
        $template->beacons()->detach();

        $template->delete();

        return response()->noContent();
    }
}
