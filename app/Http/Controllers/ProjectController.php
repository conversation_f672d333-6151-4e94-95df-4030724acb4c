<?php

namespace App\Http\Controllers;

use App\Enums\PassEventTypeEnum;
use App\Models\Project;
use App\Models\Registration;
use Illuminate\Http\Request;

class ProjectController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View
     */
    public function index()
    {
        $query = Project::query();

        return view('projects.index', [
            'projects' => $query->paginate()
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Project  $project
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View
     */
    public function show(Project $project)
    {
        $eventsQuery = $project->events();
        $registrationsQuery = Registration::whereHas('pass.template.project', fn($q) => $q->where('projects.id', $project->id));

        $installsQuery = $eventsQuery->clone()->whereType(PassEventTypeEnum::INSTALL);
        $uninstallsQuery = $eventsQuery->clone()->whereType(PassEventTypeEnum::UNINSTALL);

        $ofPeriod = function ($query, $periodStart, $periodEnd = null) {
            $query = $query->clone();
            $query->where('created_at', '>=', now()->subDays($periodStart));
            if (! is_null($periodEnd)) {
                $query->where('created_at', '<', now()->subDays($periodEnd));
            }
            return $query;
        };

        $delta = function ($stats, $type) {
            if ($stats[$type]['previous30'] == 0) return null;

            return ($stats[$type]['last30'] - $stats[$type]['previous30']) / $stats[$type]['previous30'] * 100;
        };

        $stats = [
            'installs' => [
                'last30' => $ofPeriod($installsQuery, 30)->count(),
                'previous30' => $ofPeriod($installsQuery, 60, 30)->count(),
                'total' => $installsQuery->count(),
            ],
            'uninstalls' => [
                'last30' => $ofPeriod($uninstallsQuery, 30)->count(),
                'previous30' => $ofPeriod($uninstallsQuery, 60, 30)->count(),
                'total' => $uninstallsQuery->count(),
            ],
            'registrations' => [
                'last30' => $ofPeriod($registrationsQuery, 30)->count(),
                'previous30' => $ofPeriod($registrationsQuery, 60, 30)->count(),
                'total' => $registrationsQuery->count(),
            ],
        ];

        $stats['installs']['delta'] = $delta($stats, 'installs');
        $stats['uninstalls']['delta'] = $delta($stats, 'uninstalls');
        $stats['registrations']['delta'] = $delta($stats, 'registrations');

        return view('projects.show', [
            'project' => $project,
            'stats' => $stats,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Project  $project
     * @return \Illuminate\Http\Response
     */
    public function edit(Project $project)
    {
        return view('projects.edit', [
            'project' => $project,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Project  $project
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, Project $project)
    {
        $project->update(
            $request->all()
        );

        return redirect()->back()
            ->with('notification.title', 'Saved')
            ->with('notification.body', 'Project updated!');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Project  $project
     * @return \Illuminate\Http\Response
     */
    public function destroy(Project $project)
    {
        //
    }
}
