<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class UtilsController extends Controller
{
    public function qr(Request $request)
    {
        $message = $request->input('message', 'Hello World');
        $size = $request->input('size', 100);

        return response()->streamDownload(function () use ($message, $size) {
            echo QrCode::format('png')
                ->size($size)
                ->generate($message);
        }, 'qr.png');
    }
}
