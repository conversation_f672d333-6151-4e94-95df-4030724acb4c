<?php

namespace App\Http\Middleware;

use App\Models\Pass;
use Closure;
use Illuminate\Http\Request;

class AuthApplePass
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (! $token = ($request->input('token') ?? $request->header('Authorization'))) {
            abort(401);
        }

        if (! preg_match('/ApplePass (?P<token>[a-zA-Z0-9]+)/', $token, $match)) {
            abort(401);
        }

        $token = $match['token'];

        if (! $passId = $request->route('serial_number')) {
            abort(401);
        }

        if (! Pass::withTrashed()->whereId($passId)->whereAuthorizationToken($token)->exists()) {
            abort(401);
        }

        return $next($request);
    }
}
