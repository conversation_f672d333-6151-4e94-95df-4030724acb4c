<?php

namespace App\Http\Requests\Api;

use App\Enums\FieldTypeEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;

class FieldCreateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'title' => ['required', 'max:255'],
            'key' => ['required', 'max:255', Rule::unique('fields')->where(function ($query) {
                return $query->where('project_id', $this->route('project')->id);
            })],
            'type' => ['required', 'max:255', new Enum(FieldTypeEnum::class)],
            'enum' => ['nullable', 'exclude_unless:type,'.FieldTypeEnum::ENUM->value, 'array', 'filled'],
            'required' => ['required', 'boolean'],
            'default_value' => ['nullable'],
        ];
    }
}
