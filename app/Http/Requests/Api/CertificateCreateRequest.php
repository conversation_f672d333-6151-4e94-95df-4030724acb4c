<?php

namespace App\Http\Requests\Api;

use App\Actions\MakeCertificate;
use App\Services\Apple\CertificateService;
use Illuminate\Foundation\Http\FormRequest;

class CertificateCreateRequest extends FormRequest
{
    const FILE_FIELD = 'x509';

    protected $stopOnFirstFailure = true;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            self::FILE_FIELD => ['required', 'file'],
            'is_valid' => ['required', 'accepted'],

            'team_id' => ['required'],
            'pass_type_id' => ['required'],
            'serial_number' => ['required', 'unique:certificates,serial_number'],
            'valid_from' => ['required', 'date'],
            'valid_to' => ['required', 'date', 'after:now'],

            'issued_for' => ['max:255'],
            'common_name' => ['max:255'],
            'country_code' => ['max:255'],
            'der' => ['required'],
            'pem' => ['required'],
            'p12' => ['required'],
        ];
    }

    protected function prepareForValidation()
    {
        if ($this->hasFile(self::FILE_FIELD)) {

            $derContent = $this->file(self::FILE_FIELD)->get();
            $certificateService = (new CertificateService($derContent));

            $this->merge([
                'is_valid' => $certificateService->check(),
                ...$certificateService->getAttributes(),
            ]);
        }
    }
}
