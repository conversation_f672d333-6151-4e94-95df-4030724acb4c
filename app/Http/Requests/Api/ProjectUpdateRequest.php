<?php

namespace App\Http\Requests\Api;

use App\Enums\ProjectTypeEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class ProjectUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'title' => ['max:255'],
            'type' => [new Enum(ProjectTypeEnum::class)],
            'certificate_id' => ['exists:certificates,id'],
            'location_ids' => ['array'],
            'location_ids.*' => ['exists:locations,id'],
            'location_external_ids' => ['array'],
            'location_external_ids.*' => ['exists:locations,external_id'],
            'beacon_ids' => ['array'],
            'beacon_ids.*' => ['exists:beacons,id'],
        ];
    }
}
