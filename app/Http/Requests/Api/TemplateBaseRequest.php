<?php

namespace App\Http\Requests\Api;

use App\Enums\Apple\BarcodeFormatEnum;
use App\Enums\Apple\BarcodeMessageEncodingEnum;
use App\Enums\Apple\DataDetectorTypeEnum;
use App\Enums\Apple\FieldSectionEnum;
use App\Enums\Apple\PassStyleEnum;
use App\Enums\Apple\SharingMethodEnum;
use App\Enums\Apple\TextAlignmentEnum;
use App\Enums\TargetEnum;
use Illuminate\Foundation\Http\FormRequest;

abstract class TemplateBaseRequest extends FormRequest
{
    protected function getGeneralRules($strict = false)
    {
        return [
            'project_id' => array_merge(['exists:projects,id'], $strict ? ['required'] : []),
            'target' => array_merge([TargetEnum::validationRuleIn()], $strict ? ['required'] : []),
            'style' => array_merge([PassStyleEnum::validationRuleIn()], $strict ? ['required'] : []),
            'title' => array_merge(['max:255'], $strict ? ['required'] : []),
            'description' => array_merge(['max:255'], $strict ? ['required'] : []),
            'organization_name' => array_merge(['max:255'], $strict ? ['required'] : []),
        ];
    }

    protected function getColorsRules($strict = false)
    {
        return [
            'colors' => array_merge(['array'], $strict ? ['required'] : []),
            'colors.background' => ['required_with:colors', 'color'],
            'colors.foreground' => ['required_with:colors', 'color'],
            'colors.label' => ['required_with:colors', 'color'],
        ];
    }

    protected function getBarcodeRules()
    {
        return [
            'barcode' => ['array', 'nullable'],
            'barcode.format' => ['required_with:barcode', BarcodeFormatEnum::validationRuleIn()],
            'barcode.message_encoding' => ['required_with:barcode', BarcodeMessageEncodingEnum::validationRuleIn()],
            'barcode.message' => ['required_with:barcode', 'max:255'],
            'barcode.alt_text' => ['nullable', 'max:255'],
        ];
    }

    protected function getSharingRules()
    {
        return [
            'sharing' => ['array', 'nullable'],
            'sharing.method' => ['required_with:sharing', SharingMethodEnum::validationRuleIn()],
            'sharing.url' => ['required_with:sharing'],
        ];
    }

    protected function getAssociatedAppsRules()
    {
        return [
            'associated_apps' => ['array', 'nullable'],
            'associated_apps.ios' => ['array'],
            'associated_apps.ios.*' => ['integer'],
        ];
    }

    protected function getFieldsRules()
    {
        return [
            'fields' => ['array', 'nullable'],
            'fields.*.*.attributed_value' => ['nullable', 'max:255'],
            'fields.*.*.change_message' => ['nullable', 'max:255'],
            'fields.*.*.data_detector_types' => ['nullable', 'array'],
            'fields.*.*.data_detector_types.*' => [DataDetectorTypeEnum::validationRuleIn()],
            'fields.*.*.key' => ['required', 'string', 'max:255'],
            'fields.*.*.label' => ['nullable', 'max:255'],
            'fields.*.*.text_alignment' => ['nullable', TextAlignmentEnum::validationRuleIn()],
            'fields.*.*.value' => ['required', 'max:2048'],
        ];
    }

    protected function getLocationsRules()
    {
        return [
            'location_ids' => ['array'],
            'location_ids.*' => ['exists:locations,id'],
            'location_external_ids' => ['array'],
            'location_external_ids.*' => ['exists:locations,external_id'],
        ];
    }

    protected function getBeaconsRules()
    {
        return [
            'beacon_ids' => ['array'],
            'beacon_ids.*' => ['exists:beacons,id'],
        ];
    }

    protected function prepareForValidation()
    {
        $allowedSections = FieldSectionEnum::values();

        if ($this->has('fields')) {
            $this->merge([
                'fields' => collect($this->input('fields'))->only($allowedSections)->toArray()
            ]);
        }
    }
}
