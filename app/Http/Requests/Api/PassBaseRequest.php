<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Collection;
use Illuminate\Validation\Rule;

abstract class PassBaseRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function getRules(Collection $fields, $strict = false)
    {

        $rules = [
            'fields' => ['array'],
            'external_id' => ['nullable'],
            'location_ids' => ['array'],
            'location_ids.*' => ['exists:locations,id'],
            'location_external_ids' => ['array'],
            'location_external_ids.*' => ['exists:locations,external_id'],
            'beacon_ids' => ['array'],
            'beacon_ids.*' => ['exists:beacons,id'],
            '_on_duplicate' => ['in:update,reject'],
        ];

        if ($strict) {
            $rules['fields'][] = 'required';
        }

        $fields->each(function ($field) use (&$rules, $strict) {
            $key = str_replace('.', '\\.', $field->key);
            $key = "fields.{$key}";

            $rules[$key] = $field->type->getValidationRules($field);

            if ($field->required && $strict) {
                $rules[$key][] = 'required';
            } else {
                $rules[$key][] = 'nullable';
            }
        });

        return $rules;
    }
}
