<?php

namespace App\Http\Requests\Api;

use App\Enums\FieldTypeEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;

class FieldUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'title' => ['max:255'],
            'key' => ['max:255', Rule::unique('fields')->ignore($this->route('field')->id)->where(function ($query) {
                return $query->where('project_id', $this->route('field')->project_id);
            })],
            'type' => ['required_with:enum', 'max:255', new Enum(FieldTypeEnum::class)],
            'enum' => ['nullable', 'exclude_unless:type,'.FieldTypeEnum::ENUM->value, 'array', 'filled'],
            'required' => ['required', 'boolean'],
            'default_value' => ['nullable'],
        ];
    }
}
