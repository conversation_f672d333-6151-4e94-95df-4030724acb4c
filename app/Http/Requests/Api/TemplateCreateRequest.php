<?php

namespace App\Http\Requests\Api;

class TemplateCreateRequest extends TemplateBaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return array_merge(
            $this->getGeneralRules(strict: true),
            $this->getColorsRules(strict: true),
            $this->getBarcodeRules(),
            $this->getSharingRules(),
            $this->getAssociatedAppsRules(),
            $this->getLocationsRules(),
            $this->getBeaconsRules(),
            $this->getFieldsRules(),
        );
    }
}
