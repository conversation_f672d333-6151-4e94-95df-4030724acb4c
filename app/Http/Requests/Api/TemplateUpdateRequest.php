<?php

namespace App\Http\Requests\Api;

class TemplateUpdateRequest extends TemplateBaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return array_merge(
            $this->getGeneralRules(),
            $this->getColorsRules(),
            $this->getBarcodeRules(),
            $this->getSharingRules(),
            $this->getAssociatedAppsRules(),
            $this->getLocationsRules(),
            $this->getBeaconsRules(),
            $this->getFieldsRules(),
            $this->getImagesRules(),
        );
    }

    protected function getImagesRules()
    {
        $template = $this->route('template');

        $rules = [
            'images' => ['array'],
        ];

        foreach ($template->style->getSupportedImageTypes() as $imageType) {
            $dimensions = $imageType->getDimensions();
            $dimensionsRule = sprintf('dimensions:width=%s,height=%s', ...$dimensions);
            $field = sprintf('images.%s', $imageType->value);

            $rules[$field] = ['file', 'mimes:jpeg,png,jpg', $dimensionsRule];
        }

        return $rules;
    }
}
