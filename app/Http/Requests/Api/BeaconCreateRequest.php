<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class BeaconCreateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name' => ['nullable', 'max:255'],
            'proximity_uuid' => ['required', 'uuid'],
            'major' => ['nullable', 'integer', 'min:0'],
            'minor' => ['nullable', 'integer', 'min:0'],
            'relevant_text' => ['nullable', 'max:255'],
        ];
    }
}
