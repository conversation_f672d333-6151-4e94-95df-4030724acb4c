<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class PassUpdateRequest extends PassBaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return $this->getRules(
            fields: $this->route('pass')->template->project->fields,
            strict: ! $this->isMethod('PATCH')
        );
    }
}
