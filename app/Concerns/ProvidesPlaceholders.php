<?php

namespace App\Concerns;

trait ProvidesPlaceholders
{
    abstract public function getPlaceholders($justOwn = true): array;

    public function evaluatePlaceholder($placeholder, $justOwn = true)
    {
        if (is_null($placeholder)) {
            return null;
        }

        $pattern = '${%s}';

        foreach ($this->getPlaceholders($justOwn) as $key => $value) {
            $key = sprintf($pattern, $key);

            if ($placeholder == $key) {
                return $value;
            }

            $placeholder = str_replace($key, (string) $value, $placeholder);
        }

        return $placeholder;
    }
}
