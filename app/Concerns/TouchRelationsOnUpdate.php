<?php

namespace App\Concerns;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use function Illuminate\Events\queueable;

trait TouchRelationsOnUpdate
{
    protected static function booted()
    {
        parent::booted();

        $touchMethod = function (Model $object) {
            foreach ($object->getTouchedRelations() as $relationName) {
                $related = $object->{$relationName};

                if ($related instanceof Collection) {
                    $related->each->touch();
                } else {
                    $related->touch();
                }
            }
        };

        self::updated($touchMethod);
        self::deleting($touchMethod);
    }
}
