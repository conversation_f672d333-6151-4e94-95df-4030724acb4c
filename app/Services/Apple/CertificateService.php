<?php

namespace App\Services\Apple;

use App\Concerns\NeedsTemporaryDirectory;
use App\Models\Certificate;
use Carbon\Carbon;

class CertificateService
{
    const PRIVATE_KEY_PATH = 'certificates/private.key';

    public string $der;

    public function __construct(string $derContent)
    {
        $this->der = $derContent;
    }

    public function check()
    {
        return openssl_x509_check_private_key($this->getPem(), $this->getPkey());
    }

    public function parse()
    {
        return openssl_x509_parse($this->getPem());
    }

    public function getPkcs12()
    {
        $output = null;

        try {
            openssl_pkcs12_export($this->getPem(), $output, $this->getPkey(), '');
        } catch (\Exception $e) {}

        return $output;
    }

    public function getPem()
    {
        return self::der2pem($this->der);
    }

    public function getPkey()
    {
        return file_get_contents(storage_path(self::PRIVATE_KEY_PATH));
    }

    public function getAttributes()
    {
        $data = $this->parse();

        $subject = $data['subject'];
        $serialNumber = $data['serialNumber'];
        $validFrom = new Carbon($data['validFrom_time_t']);
        $validTo = new Carbon($data['validTo_time_t']);

        return [
            'team_id' => $subject['OU'] ?? null,
            'pass_type_id' => $subject['UID'] ?? null,
            'issued_for' => $subject['O'] ?? null,
            'common_name' => $subject['CN'] ?? null,
            'country_code' => $subject['C'] ?? null,
            'serial_number' => $serialNumber,
            'valid_from' => $validFrom,
            'valid_to' => $validTo,
            'der' => $this->der,
            'pem' => $this->getPem(),
            'p12' => $this->getPkcs12(),
        ];
    }

    public static function der2pem($der_data) {
        $pem = chunk_split(base64_encode($der_data), 64, "\n");
        return "-----BEGIN CERTIFICATE-----\n".$pem."-----END CERTIFICATE-----\n";
    }

    public static function pem2der($pem_data) {
        $begin = "CERTIFICATE-----";
        $end   = "-----END";
        $pem_data = substr($pem_data, strpos($pem_data, $begin)+strlen($begin));
        $pem_data = substr($pem_data, 0, strpos($pem_data, $end));

        return base64_decode($pem_data);
    }

    public static function createCsr($email, $commonName)
    {
        $subject = [
            'commonName' => $commonName,
            'emailAddress' => $email,
        ];

        $private_key = openssl_pkey_get_private(
            file_get_contents(storage_path(self::PRIVATE_KEY_PATH))
        );

        $configargs = [
            'digest_alg' => 'sha256WithRSAEncryption'
        ];

        $csr = openssl_csr_new($subject, $private_key, $configargs);

        openssl_csr_export($csr, $csr_string);

        return $csr_string;
    }
}
