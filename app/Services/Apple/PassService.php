<?php

namespace App\Services\Apple;

use App\Enums\Apple\ImageTypeEnum;
use App\Http\Resources\Passd\Apple\PassResource;
use App\Models\Certificate;
use App\Models\Pass;
use PKPass\PKPass;

class PassService
{
    protected Pass $pass;
    protected Certificate $certificate;

    public function __construct(Pass $pass)
    {
        $this->pass = $pass;

        $this->certificate = $this->pass->template->project->certificate;
    }

    public function getData()
    {
        $data = json_decode((new PassResource($this->pass))->toJson(), true);

//        dd($data);

        return $data;
    }

    public function create()
    {
        return $this->certificate->withFiles(function ($pemPath, $p12Path) {

            $pkpass = new PKPass($p12Path);

            $pkpass->setData($this->getData());

            foreach ($this->pass->template->media as $media) {

                $imageType = ImageTypeEnum::from($media->collection_name);
                $rootImageTypeName = $imageType->getRootValue();

                $fileName = "{$rootImageTypeName}.png";
                $convertedFileNamePattern = "{$rootImageTypeName}@%s.png";

                foreach ($media->getGeneratedConversions() as $generatedConversion => $isGenerated) {

                    if ($isGenerated) {

                        $usedFilename = sprintf($convertedFileNamePattern, $generatedConversion);

                        if ($generatedConversion == '1x') {
                            $usedFilename = $fileName;
                        }

                        $pkpass->addFile(
                            $media->getPath($generatedConversion),
                            $usedFilename
                        );
                    }
                }
            }

            return $pkpass->create();
        });
    }
}
