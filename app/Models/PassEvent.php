<?php

namespace App\Models;

use App\Enums\PassEventTypeEnum;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PassEvent extends Model
{
    use HasFactory;
    use HasUuids;

    protected $guarded = [];

    protected $casts = [
        'type' => PassEventTypeEnum::class,
    ];

    public function pass()
    {
        return $this->belongsTo(Pass::class);
    }

    public function project()
    {
        return $this->belongsTo(Project::class);
    }
}
