<?php

namespace App\Models;

use App\Concerns\ProvidesPlaceholders;
use App\Concerns\TouchRelationsOnUpdate;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Location extends Model
{
    use HasFactory;
    use HasUuids;
    use TouchRelationsOnUpdate;
    use ProvidesPlaceholders;

    protected $fillable = [
        'name',
        'latitude',
        'longitude',
        'altitude',
        'relevant_text',
        'external_id',
    ];

    protected $touches = [
        'projects',
        'templates',
        'passes',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function projects()
    {
        return $this->morphedByMany(Project::class, 'locationable');
    }

    public function templates()
    {
        return $this->morphedByMany(Template::class, 'locationable');
    }

    public function passes()
    {
        return $this->morphedByMany(Pass::class, 'locationable');
    }

    public function getPlaceholders($justOwn = true): array
    {
        return [
            'location.name' => $this->name
        ];
    }
}
