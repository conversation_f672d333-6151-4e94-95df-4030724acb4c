<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Registration extends Model
{
    use HasFactory;
    use HasUuids;

    protected $fillable = [
        'device_id',
        'pass_id',
        'push_token',
    ];

    public function pass()
    {
        return $this->belongsTo(Pass::class);
    }

    public function device()
    {
        return $this->belongsTo(Device::class);
    }
}
