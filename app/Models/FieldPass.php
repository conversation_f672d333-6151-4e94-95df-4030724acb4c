<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\Pivot;

class FieldPass extends Pivot
{
    public $incrementing = true;

    public function setValueAttribute($value)
    {
        $this->attributes['value'] = serialize($value);
    }

    public function getValueAttribute()
    {
        if (is_null($this->attributes['value'])) {
            return null;
        }

        return unserialize($this->attributes['value']);
    }
}
