<?php

namespace App\Models;

use App\Concerns\TouchRelationsOnUpdate;
use App\Enums\FieldTypeEnum;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Field extends Model
{
    use HasFactory;
    use HasUuids;
    use TouchRelationsOnUpdate;

    protected $fillable = [
        'title',
        'key',
        'type',
        'enum',
        'required',
        'default_value',
        'show_in_list',
    ];

    protected $touches = ['project'];

    protected $casts = [
        'type' => FieldTypeEnum::class,
        'enum' => 'array',
        'required' => 'boolean',
    ];

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function passes()
    {
        return $this->belongsToMany(Pass::class)
            ->using(FieldPass::class)
            ->withPivot('value');
    }

    public function setDefaultValueAttribute($value)
    {
        if (is_null($value) || $value === '') {
            $this->attributes['default_value'] = null;
        } else {
            $this->attributes['default_value'] = serialize($value);
        }
    }

    public function getDefaultValueAttribute()
    {
        if (is_null($this->attributes['default_value'])) {
            return null;
        }

        return unserialize($this->attributes['default_value']);
    }
}
