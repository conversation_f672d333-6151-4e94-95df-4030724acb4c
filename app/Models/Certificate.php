<?php

namespace App\Models;

use App\Concerns\NeedsTemporaryDirectory;
use App\Enums\CertificateWarningLevelEnum;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Certificate extends Model
{
    use HasFactory;
    use HasUuids;
    use NeedsTemporaryDirectory;

    protected $dates = [
        'valid_from',
        'valid_to',
    ];

    protected $fillable = [
        'serial_number',
        'team_id',
        'pass_type_id',
        'issued_for',
        'common_name',
        'country_code',
        'der',
        'pem',
        'p12',
        'valid_from',
        'valid_to',
    ];

    protected $hidden = [
        'der',
        'pem',
        'p12',
    ];

    public function getExpiresInAttribute($format = 'human')
    {
        return match($format) {
            'machine' => $this->valid_to->diffInDays(now(), false),
            default => $this->valid_to->diffForHumans(),
        };
    }

    public function getWarningLevelAttribute(): CertificateWarningLevelEnum
    {
        $expiresIn = $this->getExpiresInAttribute('machine');

        if ($expiresIn < -30) return CertificateWarningLevelEnum::GREEN;
        if ($expiresIn < 0) return CertificateWarningLevelEnum::ORANGE;

        return CertificateWarningLevelEnum::RED;
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function withFiles($callback)
    {
        self::withTemporaryDirectory(function ($temporaryDirectory) use ($callback, &$output) {
            $pemPath = $temporaryDirectory->path('certificate.pem');
            $p12Path = $temporaryDirectory->path('certificate.p12');

            file_put_contents($pemPath, $this->pem);
            file_put_contents($p12Path, $this->p12);

            $output = $callback($pemPath, $p12Path);
        });

        return $output;
    }
}
