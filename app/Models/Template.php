<?php

namespace App\Models;

use App\Concerns\TouchRelationsOnUpdate;
use App\Enums\Apple\ImageTypeEnum;
use App\Enums\Apple\PassStyleEnum;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Image\Manipulations;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Template extends Model implements HasMedia
{
    use HasFactory;
    use HasUuids;
    use InteractsWithMedia;
    use TouchRelationsOnUpdate;

    protected $casts = [
        'colors' => 'json',
        'barcode' => 'json',
        'sharing' => 'json',
        'associated_apps' => 'json',
        'fields' => 'json',
        'style' => PassStyleEnum::class,
        'target' => \App\Enums\TargetEnum::class,
    ];

    protected $fillable = [
        'project_id',
        'target',
        'style',
        'title',
        'description',
        'organization_name',
        'colors',
        'barcode',
        'sharing',
        'associated_apps',
        'fields',
    ];

    protected $touches = ['passes'];

    public function registerMediaCollections(): void
    {
        foreach (ImageTypeEnum::values() as $value) {
            $this->addMediaCollection($value)->singleFile();
        }
    }

    public function registerMediaConversions(Media $media = null): void
    {
        foreach (ImageTypeEnum::cases() as $case) {
            $case->registerMediaConversions($this);
        }
    }

    public function passes()
    {
        return $this->hasMany(Pass::class);
    }

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function fields()
    {
        return $this->hasMany(Field::class);
    }

    public function locations()
    {
        return $this->morphToMany(Location::class, 'locationable');
    }

    public function beacons()
    {
        return $this->morphToMany(Beacon::class, 'beaconable');
    }
}
