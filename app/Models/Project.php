<?php

namespace App\Models;

use App\Concerns\ProvidesPlaceholders;
use App\Concerns\TouchRelationsOnUpdate;
use App\Enums\ProjectTypeEnum;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Project extends Model
{
    use HasFactory;
    use HasUuids;
    use TouchRelationsOnUpdate;
    use ProvidesPlaceholders;

    protected $fillable = [
        'title',
        'type',
        'certificate_id',
    ];

    protected $casts = [
        'type' => ProjectTypeEnum::class,
    ];

    protected $touches = [
        'templates',
    ];

    public function certificate()
    {
        return $this->belongsTo(Certificate::class);
    }

    public function templates()
    {
        return $this->hasMany(Template::class);
    }

    public function locations()
    {
        return $this->morphToMany(Location::class, 'locationable');
    }

    public function beacons()
    {
        return $this->morphToMany(Beacon::class, 'beaconable');
    }

    public function fields()
    {
        return $this->hasMany(Field::class);
    }

    public function passes()
    {
        return $this->hasManyThrough(Pass::class, Template::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function events()
    {
        return $this->hasMany(PassEvent::class);
    }

    public function getPlaceholders($justOwn = true): array
    {
        return $this->fields->pluck('default_value', 'key')->toArray();
    }

    public function setExternalIdFieldIdAttribute($value)
    {
        if ($value === '') {
            $value = null;
        }

        $this->attributes['external_id_field_id'] = $value;
    }

    public function externalIdField()
    {
        return $this->belongsTo(Field::class, 'external_id_field_id');
    }
}
