<?php

namespace App\Models;

use App\Concerns\ProvidesPlaceholders;
use App\Concerns\TouchRelationsOnUpdate;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Beacon extends Model
{
    use HasFactory;
    use HasUuids;
    use TouchRelationsOnUpdate;
    use ProvidesPlaceholders;

    protected $fillable = [
        'name',
        'proximity_uuid',
        'major',
        'minor',
        'relevant_text',
    ];

    protected $touches = [
        'projects',
        'templates',
        'passes',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function projects()
    {
        return $this->morphedByMany(Project::class, 'beaconable');
    }

    public function templates()
    {
        return $this->morphedByMany(Template::class, 'beaconable');
    }

    public function passes()
    {
        return $this->morphedByMany(Pass::class, 'beaconable');
    }

    public function getPlaceholders($justOwn = true): array
    {
        return [
            'beacon.name' => $this->name,
        ];
    }
}
