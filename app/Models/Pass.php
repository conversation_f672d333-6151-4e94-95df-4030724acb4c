<?php

namespace App\Models;

use App\Concerns\ProvidesPlaceholders;
use App\Jobs\Passd\SendPush;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Routing\UrlGenerator;
use Illuminate\Support\Str;

class Pass extends Model
{
    use HasFactory;
    use HasUuids;
    use SoftDeletes;
    use ProvidesPlaceholders;

    protected $casts = [
        'expires_at' => 'datetime',
    ];

    protected $fillable = [
        'external_id',
    ];

    protected static function booted()
    {
        parent::booted();

        self::creating(function ($object) {
            $object->authorization_token = $object->authorization_token ?? Str::random(32);
        });

        self::updated(function ($object) {
            SendPush::dispatch($object);
        });

        self::deleting(function ($object) {
            SendPush::dispatch($object);
        });
    }

    public function registrations()
    {
        return $this->hasMany(Registration::class);
    }

    public function template()
    {
        return $this->belongsTo(Template::class);
    }

    public function locations()
    {
        return $this->morphToMany(Location::class, 'locationable');
    }

    public function beacons()
    {
        return $this->morphToMany(Beacon::class, 'beaconable');
    }

    public function fields()
    {
        return $this->belongsToMany(Field::class)
            ->using(FieldPass::class)
            ->withPivot('value');
    }

    public function events()
    {
        return $this->hasMany(PassEvent::class);
    }

    public function getInstallUrlAttribute()
    {
        app(UrlGenerator::class)->forceScheme('https');
        app(UrlGenerator::class)->forceRootUrl(config('app.public_url'));

        $url = route('passd.passes.index', [
            'pass_type_id' => $this->template->project->certificate->pass_type_id,
            'serial_number' => $this->id,
            'token' => "ApplePass {$this->authorization_token}",
        ]);

        app(UrlGenerator::class)->forceScheme(null);
        app(UrlGenerator::class)->forceRootUrl(null);

        return $url;
    }

    public function getPlaceholders($justOwn = true): array
    {
        $placeholders = $this->fields->pluck('pivot.value', 'key')->toArray();

        if (! $justOwn) {
            return array_merge(
                $this->template->project->getPlaceholders(),
                $placeholders,
            );
        }

        return $placeholders;
    }
}
