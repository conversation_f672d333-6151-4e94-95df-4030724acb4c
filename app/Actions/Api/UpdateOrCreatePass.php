<?php

namespace App\Actions\Api;

use App\Models\Pass;
use App\Models\Project;
use App\Models\Template;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateOrCreatePass
{
    use AsAction;

    public function handle(Project $project, Template $template, $passData = [], $onDuplicate = 'reject', Pass $pass = null)
    {
        if (!isset($passData['external_id']) && ($eField = $project->externalIdField)) {
            $passData['external_id'] = $passData['fields'][$eField->key] ?? null;
        }

        if (!$pass && isset($passData['external_id'])) {
            $pass = $template->passes()->firstWhere([
                'external_id' => $passData['external_id'],
            ]);

            if (isset($pass) && $onDuplicate != 'update') {
                return null;
            }
        }

        if (isset($pass)) {
            $pass->update($passData);
        } else {
            $pass = $template->passes()->create($passData);
        }

        return $pass;
    }
}
