<?php

namespace App\Actions\Api;

use App\Models\Pass;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateFieldsInPass
{
    use AsAction;

    public function handle(Pass $pass, $fields, $patch = false)
    {
        if (is_null($fields)) return;

        $fields = $pass->template->project->fields->mapWithKeys(function ($field) use ($fields) {
            $value = $fields[$field->key] ?? null;
            return [$field->id => $value];
        })->filter(function ($value) {
            return !is_null($value);
        })->toArray();

        if ($patch) {
            foreach ($fields as $key => $value) {
                if ($pass->fields()->where('fields.id', $key)->exists()) {
                    $pass->fields()->updateExistingPivot($key, ['value' => $value]);
                } else {
                    $pass->fields()->attach($key, ['value' => $value]);
                }
            }
        } else {
            $pass->fields()->sync(
                collect($fields)->map(fn ($value) => ['value' => $value])->toArray()
            );
        }

        if (is_null($pass->external_id) && !is_null($eFieldId = $pass->template->project->external_id_field_id)) {
            $eField = $pass->fields()->where('fields.id', $eFieldId)->first();

            if ($eField) {
                $pass->external_id = $eField->pivot->value;
                $pass->save();
            }
        }
    }
}
