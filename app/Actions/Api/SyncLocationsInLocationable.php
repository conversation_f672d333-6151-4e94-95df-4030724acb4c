<?php

namespace App\Actions\Api;

use App\Models\Location;
use Lorisleiva\Actions\Concerns\AsAction;

class SyncLocationsInLocationable
{
    use AsAction;

    public function handle($locationable, $locationIds = [], $locationExternalIds = [])
    {
        if (is_array($locationIds)) {
            $locationable->locations()->sync($locationIds);
            return;
        }

        if (is_array($locationExternalIds)) {
            $locations = Location::whereIn('external_id', $locationExternalIds)->pluck('id');
            $locationable->locations()->sync($locations);
            return;
        }
    }
}
