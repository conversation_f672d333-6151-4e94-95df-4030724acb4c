<?php

namespace App\Actions\Api;

use App\Enums\Apple\ImageTypeEnum;
use App\Models\Template;
use Lorisleiva\Actions\Concerns\AsAction;

class AddImagesToTemplate
{
    use AsAction;

    public function handle(Template $template, $images)
    {
        if (empty($images)) return;

        foreach ($images as $imageType => $file) {

            $rootType = ImageTypeEnum::tryFrom($imageType)->getRootValue();

            $template->media()
                ->where('custom_properties->root_type', $rootType)
                ->each(fn($medium) => $medium->delete());

            $template
                ->addMedia($file)
                ->withCustomProperties(['root_type' => $rootType])
                ->toMediaCollection($imageType);
        }
    }
}
