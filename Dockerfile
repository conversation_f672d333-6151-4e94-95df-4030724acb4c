FROM registry.228.by/sola:8.1-octane

COPY . .

ADD https://raw.githubusercontent.com/mlocati/docker-php-extension-installer/master/install-php-extensions /usr/local/bin/

RUN echo "post_max_size = 50M" >> /usr/local/etc/php/conf.d/gbma.ini \
    && echo "upload_max_filesize = 50M" >> /usr/local/etc/php/conf.d/gbma.ini

RUN chmod uga+x /usr/local/bin/install-php-extensions && sync && \
    install-php-extensions imagick

COPY --chown=www-data ./ .
RUN chmod -R 777 /var/www/html/storage

RUN composer install --prefer-dist && \
    npm ci --quiet --unsafe-perm && \
    npm run build && \
    php artisan storage:link

RUN chown -R www-data:www-data .

CMD ["php", "artisan", "octane:start", "--host=0.0.0.0"]
