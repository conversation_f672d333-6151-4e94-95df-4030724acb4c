{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1", "ext-openssl": "*", "duccio/apns-php": "^1.0", "guzzlehttp/guzzle": "^7.2", "kaishiyoku/laravel-color-validation": "^2.1", "laravel/framework": "^9.19", "laravel/jetstream": "^2.12", "laravel/octane": "^1.3", "laravel/sanctum": "^3.0", "laravel/telescope": "^4.9", "laravel/tinker": "^2.7", "livewire/livewire": "^2.5", "lorisleiva/laravel-actions": "^2.4", "pkpass/pkpass": "^2.0", "sentry/sentry-laravel": "^3.1", "simplesoftwareio/simple-qrcode": "^4.2", "spatie/laravel-medialibrary": "^10.7", "spatie/temporary-directory": "^2.1", "spiral/roadrunner": "^2.8.2", "wpb/string-blade-compiler": "^6.0"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan key:generate-private --quiet"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}