ifneq (,$(wildcard ../.env.$(BITBUCKET_DEPLOYMENT_ENVIRONMENT)))
    include ../.env.$(BITBUCKET_DEPLOYMENT_ENVIRONMENT)
endif

include environment-variables.mk
include variables.mk
include executables.mk

export

deploy: deploy-non-production
deploy-non-production: publish-docker-compose-yml cleanup-dst-path upload-deployment-assets down create-database up fresh-seed telegram
deploy-production: publish-docker-compose-yml upload-deployment-assets up migrate seed telegram

publish-docker-compose-yml:
ifneq ("$(wildcard $(docker-compose-yml-template))","")
	@envsubst < $(docker-compose-yml-template) > docker-compose.yml
else
	$(error $(docker-compose-yml-template) not exists)
endif

cleanup-dst-path:
	$(call ssh-exec, rm -rf $(dst-path))

upload-deployment-assets:
	$(call ssh-exec, mkdir -p $(dst-path))
	$(call ssh-cp, docker-compose.yml)
	$(call ssh-cp, ../.env.$(BITBUCKET_DEPLOYMENT_ENVIRONMENT))
	$(call ssh-exec, cp $(dst-path)/.env.$(BITBUCKET_DEPLOYMENT_ENVIRONMENT) $(dst-path)/.env)

drop-database:
	$(call ssh-exec, $(mysql-exec) "DROP DATABASE IF EXISTS \`$(DB_DATABASE)\`;")

create-database:
	$(call ssh-exec, $(mysql-exec) "CREATE DATABASE IF NOT EXISTS \`$(DB_DATABASE)\` character set utf8mb4 collate utf8mb4_unicode_ci;")

drop-database-docker:
	$(call ssh-exec, $(docker-compose-exec) $(mysql-exec) "DROP DATABASE IF EXISTS \`$(DB_DATABASE)\`;")

create-database-docker:
	$(call ssh-exec, $(docker-compose-exec) $(mysql-exec) "CREATE DATABASE IF NOT EXISTS \`$(DB_DATABASE)\` character set utf8mb4 collate utf8mb4_unicode_ci;")

migrate:
	$(call ssh-exec, $(docker-compose-exec) php artisan migrate --no-interaction --force)

seed:
	$(call ssh-exec, $(docker-compose-exec) php artisan db:seed --no-interaction --force)

fresh-seed:
	$(call ssh-exec, $(docker-compose-exec) php artisan migrate:fresh --seed --no-interaction --force)

up:
	$(call ssh-exec, $(docker-compose) pull)
	$(call ssh-exec, $(docker-compose) up -d)

stack-deploy:
	$(call ssh-exec, docker stack deploy -c <(docker-compose config) pkpass)
	$(call ssh-exec, $(docker-compose) config | docker stack deploy -c - pkpass)

stack-migrate:
	$(call ssh-exec, docker pull $(APP_IMAGE))
	$(call ssh-exec, $(docker-run-stack) -c "php artisan migrate --no-interaction --force")

stack-seed:
	$(call ssh-exec, $(docker-run-stack) -c "php artisan db:seed --no-interaction --force")

down:
	$(call ssh-exec, $(docker-compose) down --remove-orphans)

telegram:
	$(call telegram-notify, $(BOT_MESSAGE))
