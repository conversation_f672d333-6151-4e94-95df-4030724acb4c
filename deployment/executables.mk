# Executables
DOCKER_COMPOSE = $$(docker compose version >/dev/null 2>&1 && echo docker compose || echo docker-compose)

mysql-exec = mysql -u$(DB_USERNAME) -h$(DB_HOST) -e
docker-compose      = $(DOCKER_COMPOSE)
docker-compose-exec = $(DOCKER_COMPOSE) exec -T pkpass_app
docker-run-stack = docker run --rm -i --entrypoint bash --add-host host.docker.internal:host-gateway -e APP_ENV=$(BITBUCKET_DEPLOYMENT_ENVIRONMENT) -e CACHE_DRIVER=file -e QUEUE_CONNECTION=sync -e SCOUT_DRIVER=null $(APP_IMAGE)

SSH_FLAGS = -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o LogLevel=ERROR

define ssh-exec
	ssh $(SSH_FLAGS) $(SSH_USER)@$(SERVER) -p $(SERVER_PORT) 'cd $(dst-path) 2> /dev/null; $(1)'
endef

define ssh-cp
	scp -P $(SERVER_PORT) $(SSH_FLAGS) -rp $(1) $(SSH_USER)@$(SERVER):$(dst-path)/
endef

define telegram-notify
	curl -m 10 -g "https://api.telegram.org/bot$(BOT_TOKEN)/sendMessage?chat_id=$(CHAT_ID)&text=$(1)&parse_mode=HTML&disable_web_page_preview=true"
endef
