# Environment variables

PROJECT_FOLDER_NAME = $(shell basename $(dir $(abspath $(dir $$PWD))))

TLD ?= 228.by
SSH_USER ?= root
BITBUCKET_REPO_SLUG ?= $(PROJECT_FOLDER_NAME)
BITBUCKET_BRANCH ?= master
DOCKER_REGISTRY ?= registry.$(TLD)

SERVER_PORT ?= 22

REPO_BRANCH_SLUG = $(subst .,-,$(BITBUCKET_REPO_SLUG)-$(BITBUCKET_BRANCH)-$(BITBUCKET_DEPLOYMENT_ENVIRONMENT))

APP_IMAGE = $(DOCKER_REGISTRY)/$(BITBUCKET_REPO_SLUG):$(BITBUCKET_BRANCH)
STATIC_PROXY_IMAGE = $(DOCKER_REGISTRY)/$(BITBUCKET_REPO_SLUG):static-proxy

APP_DOMAIN ?= $(R<PERSON><PERSON>_<PERSON>ANCH_SLUG).$(TLD)
APP_URL ?= https://$(APP_DOMAIN)
DB_DATABASE ?= $(REPO_<PERSON>ANCH_SLUG)

BOT_MESSAGE = <b>[$(BITBUCKET_PROJECT_KEY)]</b>\
							Build <a href=\"https://bitbucket.org/$(BITBUCKET_REPO_FULL_NAME)/addon/pipelines/home%23!/results/$(BITBUCKET_BUILD_NUMBER)\">%23$(BITBUCKET_BUILD_NUMBER)</a>\
							in <i>$(BITBUCKET_REPO_SLUG)</i> was deployed to <code>$(BITBUCKET_DEPLOYMENT_ENVIRONMENT)</code>:\
							$(APP_URL)
