version: "3.4"

x-app: &app
  image: ${APP_IMAGE}
  restart: unless-stopped
  environment:
    APP_ENV: test
  depends_on:
    - redis
    - meilisearch
  volumes:
    - storage:/var/www/html/storage/app
  networks:
    - backend
  extra_hosts:
    - "host.docker.internal:host-gateway"

services:
  app:
    <<: *app
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik"
      - "traefik.http.routers.${REPO_BRANCH_SLUG}.rule=Host(`${APP_DOMAIN}`)"
    networks:
      - backend
      - traefik

  scheduler:
    <<: *app
    command: php artisan schedule:work

  worker:
    <<: *app
    command: php artisan queue:work

  redis:
    image: redis:alpine
    restart: unless-stopped
    command: sh -c 'rm -f /data/dump.rdb && redis-server --save "" --appendonly no'
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      retries: 3
      timeout: 5s
    networks:
      - backend

  meilisearch:
    image: getmeili/meilisearch@sha256:d7fff5276b6de80defb749948ef8a35988360dcaa922f8345f0e3315f4d6f525
    restart: unless-stopped
    networks:
      - backend

volumes:
  storage:

networks:
  traefik:
    external: true
  backend:
