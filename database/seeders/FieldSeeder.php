<?php

namespace Database\Seeders;

use App\Enums\FieldTypeEnum;
use App\Models\Project;
use Illuminate\Database\Seeder;

class FieldSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $project = Project::find('00000000-0000-0000-0000-000000000000');
        $project->fields()->updateOrCreate([
            'id' => '10000000-0000-0000-0000-000000000015',
        ], [
            'id' => '10000000-0000-0000-0000-000000000015',
            'title' => 'Имя владельца карты',
            'key' => 'customer.first_name',
            'type' => FieldTypeEnum::STRING,
            'required' => true,
        ]);

        $project->fields()->updateOrCreate([
            'id' => '10000000-0000-0000-0000-000000000016',
        ], [
            'id' => '10000000-0000-0000-0000-000000000016',
            'title' => 'Отчество владельца карты',
            'key' => 'customer.middle_name',
            'type' => FieldTypeEnum::STRING,
            'required' => false,
        ]);

        $project->fields()->updateOrCreate([
            'id' => '10000000-0000-0000-0000-000000000017',
        ], [
            'id' => '10000000-0000-0000-0000-000000000017',
            'title' => 'Фамилия владельца карты',
            'key' => 'customer.last_name',
            'type' => FieldTypeEnum::STRING,
            'required' => false,
        ]);


        $project->fields()->updateOrCreate([
            'id' => '10000000-0000-0000-0000-000000000018',
        ], [
            'id' => '10000000-0000-0000-0000-000000000018',
            'title' => 'Баланс бонусного счета',
            'key' => 'bonuses',
            'type' => FieldTypeEnum::NUMERIC,
            'required' => true,
        ]);

        $externalField = $project->fields()->updateOrCreate([
            'id' => '10000000-0000-0000-0000-000000000019',
        ], [
            'id' => '10000000-0000-0000-0000-000000000019',
            'title' => 'Номер карты лояльности',
            'key' => 'card_number',
            'type' => FieldTypeEnum::NUMERIC,
            'required' => true,
        ]);

        $project->externalIdField()->associate($externalField);
        $project->save();
    }
}
