<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $this->call([
            UserSeeder::class,
            CertificateSeeder::class,
            ProjectSeeder::class,
            FieldSeeder::class,
            TemplateSeeder::class,
        ]);

        if (app()->environment('local', 'test')) {
            $this->call(Demo\DatabaseSeeder::class);
        }
    }
}
