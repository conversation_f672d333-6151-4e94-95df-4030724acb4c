<?php

namespace Database\Seeders\Demo;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Symfony\Component\Uid\Uuid;

class BeaconSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $user = User::first();

        $beacon = $user->beacons()->create([
            'id' => '00000000-0000-0000-0000-000000000000',
            'name' => 'Beacon на Хоружей',
            'proximity_uuid' => Uuid::v4(),
            'relevant_text' => '${customer.name}, в ${beacon.name} сегодня праздник!',
        ]);
    }
}
