<?php

namespace Database\Seeders\Demo;

use App\Models\Project;
use App\Services\Apple\PassService;
use Illuminate\Database\Seeder;

class PassSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $project1 = Project::find('00000000-0000-0000-0000-000000000000');
        $project2 = Project::find('00000000-0000-0000-0000-000000000001');
        $project3 = Project::find('00000000-0000-0000-0000-000000000002');

        $certificate1 = $project1->certificate;
        $certificate2 = $project2->certificate;
        $certificate3 = $project3->certificate;

        $template1 = $project1->templates()->find('00000000-0000-0000-0000-000000000000');
        $template2 = $project2->templates()->find('00000000-0000-0000-0000-000000000001');
        $template3 = $project3->templates()->find('00000000-0000-0000-0000-000000000002');

        $pass1 = $template1->passes()->create([
            'id' => '00000000-0000-0000-0000-000000000000',
            'authorization_token' => '1234567890abcdef',
        ]);

        $pass2 = $template2->passes()->create([
            'id' => '00000000-0000-0000-0000-000000000001',
            'authorization_token' => '1234567890abcdef',
        ]);

        $pass3 = $template2->passes()->create([
            'id' => '00000000-0000-0000-0000-000000000002',
            'authorization_token' => '1234567890abcdef',
        ]);

        $fields = [
            'customer.first_name' => 'Артур',
            'customer.last_name' => 'Горх',
//            'customer.middle_name' => 'Анатольевич',
            'customer.balance' => 15.64,
            'card.number' => '2269224204436',
        ];

        foreach ($fields as $key => $value) {
            $field1 = $project1->fields()->where('key', $key)->first();
            $field2 = $project2->fields()->where('key', $key)->first();
            $field3 = $project3->fields()->where('key', $key)->first();

            if ($field2) {
                $pass1->fields()->attach($field1->id, ['value' => $value]);
            }

            if ($field2) {
                $pass2->fields()->attach($field2->id, ['value' => $value]);
            }

            if ($field3) {
                $pass3->fields()->attach($field3->id, ['value' => $value]);
            }

        }

        $generator1 = new PassService($pass1);
        $generator2 = new PassService($pass2);
        $generator3 = new PassService($pass3);

        file_put_contents(public_path("{$certificate1->pass_type_id}1.pkpass"), $generator1->create());
        file_put_contents(public_path("{$certificate2->pass_type_id}2.pkpass"), $generator2->create());
        file_put_contents(public_path("{$certificate3->pass_type_id}3.pkpass"), $generator3->create());
    }
}
