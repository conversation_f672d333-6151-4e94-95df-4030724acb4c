<?php

namespace Database\Seeders\Demo;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class LocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $user = User::first();

        $location = $user->locations()->create([
            'id' => '00000000-0000-0000-0000-000000000000',
            'name' => 'Локация на Независимости',
            'latitude' => 53.935910,
            'longitude' => 27.652040,
            'relevant_text' => '${customer.name}, в ${location.name} сегодня скидка 20% на молоко и еще много на что еще сегодня скидка 20% на молоко и еще много на что еще',
            'external_id' => 12345,
        ]);
    }
}
