<?php

namespace Database\Seeders\Demo;

use App\Enums\PassEventTypeEnum;
use App\Models\Device;
use App\Models\Pass;
use App\Models\PassEvent;
use App\Models\Registration;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Lottery;

class RegistrationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $device = Device::updateOrCreate(['uid' => 'a6c0a970789bcaf4b8241072066878df']);

        Registration::create([
            'id' => '00000000-0000-0000-0000-000000000000',
            'pass_id' => Pass::first()->id,
            'device_id' => $device->id,
            'push_token' => '1abf08ee80830aac3669bfd047d591eb6dfb28838c5f675651bca9195d2eca97',
        ]);

        $passes = Pass::with('template.project')->get();

        for ($i = 0; $i < 100000; $i++) {
            $date = now()->subDays(mt_rand(0, 60));
            $event = collect(PassEventTypeEnum::values())->random();
            $pass = $passes->random();

            PassEvent::create([
                'pass_id' => $pass->id,
                'project_id' => $pass->template->id,
                'type' => $event,
                'created_at' => $date,
            ]);
        }
    }
}
