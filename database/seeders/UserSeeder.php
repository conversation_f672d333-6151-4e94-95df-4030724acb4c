<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $user = User::updateOrCreate(['id' => '00000000-0000-0000-0000-000000000000'], [
            'id' => '00000000-0000-0000-0000-000000000000',
            'email' => '<EMAIL>',
            'name' => 'First User',
            'password' => 'password',
        ]);

        DB::table('personal_access_tokens')->updateOrInsert([
            'tokenable_type' => User::class,
            'tokenable_id' => $user->id,
            'name' => 'postman',
        ], [
            'id' => 1,
            'tokenable_type' => User::class,
            'tokenable_id' => $user->id,
            'name' => 'postman',
            'token' => '0d21483d1079804ac11c694eb22ef69ecae27cb9a15797a3349cf01f4e2946e6', // 1|ED4IJWDoJGY44nmJJbY3KA5gx8hqSgCtYs3h3sD8
            'abilities' => '["*"]',
        ]);
    }
}
