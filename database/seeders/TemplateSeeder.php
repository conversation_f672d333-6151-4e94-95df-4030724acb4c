<?php

namespace Database\Seeders;

use App\Enums\Apple\BarcodeFormatEnum;
use App\Enums\Apple\BarcodeMessageEncodingEnum;
use App\Enums\Apple\FieldSectionEnum;
use App\Enums\Apple\ImageTypeEnum;
use App\Enums\Apple\NumberStyleEnum;
use App\Enums\Apple\PassStyleEnum;
use App\Enums\Apple\SharingMethodEnum;
use App\Enums\TargetEnum;
use App\Models\Project;
use App\Models\Template;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Storage;

class TemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        echo "TemplateSeeder: Starting...\n";

        $project = Project::find('00000000-0000-0000-0000-000000000000');

        if (!$project) {
            throw new \Exception('Project with ID 00000000-0000-0000-0000-000000000000 not found');
        }

        echo "TemplateSeeder: Project found, creating template...\n";

        $yrTemplate = Template::updateOrCreate([
            'id' => '00000000-0000-0000-0000-000000000000',
        ], [
            'id' => '00000000-0000-0000-0000-000000000000',
            'project_id' => $project->id,
            'target' => TargetEnum::APPLE_PKPASS,
            'style' => PassStyleEnum::STORE_CARD,
            'title' => 'Cистема лояльности',
            'organization_name' => 'Yves rocher',
            'description' => 'Cистема лояльности',
            'colors' => [
                "background" => "rgb(255,255,255)",
                "foreground" => "rgb(125,143,62)",
                "label" => "rgb(125,143,62)",
            ],
            'barcode' => [
                "alt_text" => '${card_number}',
                "format" => BarcodeFormatEnum::CODE128,
                "message" => '${card_number}',
                "message_encoding" => BarcodeMessageEncodingEnum::ISO_8859_1,
            ],
            'sharing' => [
                "method" => SharingMethodEnum::URL,
                "url" => 'https://www.y-r.by/personal/bonus-account'
            ],
            'associated_apps' => [
                'ios' => [**********],
            ],
            'fields' => [
                ...$this->makeFields(FieldSectionEnum::HEADER, [
                    $this->makeField(
                        key: 'bonuses',
                        changeMessage: 'Ваш баланс составляет %@',
                        label: 'Баланс',
                        numberStyle: NumberStyleEnum::DECIMAL,
                    )
                ]),
                ...$this->makeFields(FieldSectionEnum::BACK, [
                    $this->makeField(
                        key: 'customer.first_name',
                        label: 'Владелец карты',
                        value: '${customer.first_name} ${customer.last_name}',
                    ),
                    $this->makeField(
                        key: 'card_number',
                        label: 'Номер карты лояльности',
                    ),
                ]),
            ],
        ]);

        echo "TemplateSeeder: Template created, adding images...\n";

        $yrImages = [
            'yr/icon.png' => ImageTypeEnum::ICON,
            'yr/logo.png' => ImageTypeEnum::LOGO,
            'yr/strip.png' => ImageTypeEnum::STORE_CARD_STRIP,
        ];

        collect($yrImages)->each(function ($imageType, $imageName) use ($yrTemplate) {
            try {
                echo "TemplateSeeder: Processing image {$imageName}...\n";
                $input = storage_path("initial/{$imageName}");

                if (!file_exists($input)) {
                    throw new \Exception("Image file not found: {$input}");
                }

                $yrTemplate->addMedia($input)
                    ->preservingOriginal()
                    ->withCustomProperties(['root_type' => $imageType->getRootValue()])
                    ->toMediaCollection($imageType->value);

                echo "TemplateSeeder: Image {$imageName} processed successfully\n";
            } catch (\Exception $e) {
                echo "TemplateSeeder: Error processing image {$imageName}: " . $e->getMessage() . "\n";
                throw $e;
            }
        });

        echo "TemplateSeeder: Images added, sleeping...\n";

        sleep(5);

        echo "TemplateSeeder: Completed successfully!\n";

    }

    protected function makeFields(FieldSectionEnum $section, $fields)
    {
        return [$section->value => $fields];
    }

    protected function makeField(
        $key, $value = null, $attributedValue = null, $changeMessage = null, $dataDetectorTypes = null,
        $label = null, $textAlignment = null, $dateStyle = null, $numberStyle = null
    ) {
        $field = [
            'key' => $key,
            'value' => $value ?? sprintf('${%s}', $key),
            'attributed_value' => $attributedValue,
            'change_message' => $changeMessage,
            'data_detector_types' => $dataDetectorTypes,
            'label' => $label,
            'text_alignment' => $textAlignment,
            'date_style' => $dateStyle,
            'number_style' => $numberStyle,
        ];

        return collect($field)->filter()->toArray();
    }
}
