<?php

namespace Database\Seeders;

use App\Enums\ProjectTypeEnum;
use App\Models\Certificate;
use App\Models\Project;
use App\Enums\FieldTypeEnum;
use App\Models\User;
use Illuminate\Database\Seeder;

class ProjectSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $project1 = Project::updateOrCreate([
            'id' => '00000000-0000-0000-0000-000000000000',
        ], [
            'title' => 'Yves Rocher',
            'type' => ProjectTypeEnum::LOYALTY,
            'certificate_id' => '00000000-0000-0000-0000-000000000000',
            'user_id' => User::first()->id,
        ]);
        $project1->fields()->updateOrCreate([
            'id' => '10000000-0000-0000-0000-000000000015',
        ], [
            'id' => '10000000-0000-0000-0000-000000000015',
            'title' => 'Имя владельца карты',
            'key' => 'customer.first_name',
            'type' => FieldTypeEnum::STRING,
            'required' => true,
        ]);

        $project1->fields()->updateOrCreate([
            'id' => '10000000-0000-0000-0000-000000000016',
        ], [
            'id' => '10000000-0000-0000-0000-000000000016',
            'title' => 'Отчество владельца карты',
            'key' => 'customer.middle_name',
            'type' => FieldTypeEnum::STRING,
            'required' => false,
        ]);

        $project1->fields()->updateOrCreate([
            'id' => '10000000-0000-0000-0000-000000000017',
        ], [
            'id' => '10000000-0000-0000-0000-000000000017',
            'title' => 'Фамилия владельца карты',
            'key' => 'customer.last_name',
            'type' => FieldTypeEnum::STRING,
            'required' => false,
        ]);


        $project1->fields()->updateOrCreate([
            'id' => '10000000-0000-0000-0000-000000000018',
        ], [
            'id' => '10000000-0000-0000-0000-000000000018',
            'title' => 'Баланс бонусного счета',
            'key' => 'bonuses',
            'type' => FieldTypeEnum::NUMERIC,
            'required' => true,
        ]);

        $externalField = $project1->fields()->updateOrCreate([
            'id' => '10000000-0000-0000-0000-000000000019',
        ], [
            'id' => '10000000-0000-0000-0000-000000000019',
            'title' => 'Номер карты лояльности',
            'key' => 'card_number',
            'type' => FieldTypeEnum::NUMERIC,
            'required' => true,
        ]);

        $project1->externalIdField()->associate($externalField);
        $project1->save();
    }
}
