<?php

namespace Database\Seeders;

use App\Models\User;
use App\Services\Apple\CertificateService;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Storage;

class CertificateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $user = User::first();

        $certificate = (new CertificateService(
            Storage::disk('demo')->get('yr.cer')
        ))->getAttributes();

        $certificate['id'] = '00000000-0000-0000-0000-000000000000';

        $user->certificates()->updateOrCreate(['id' => $certificate['id']], $certificate);
    }
}
