<?php

use App\Enums\Apple\PassStyleEnum;
use App\Enums\TargetEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('templates', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('project_id')->constrained()->cascadeOnDelete();
            $table->enum('target', TargetEnum::values());
            $table->enum('style', PassStyleEnum::values());
            $table->string('title');
            $table->string('description')->nullable();
            $table->string('organization_name');
            $table->json('colors')->nullable();
            $table->json('barcode')->nullable();
            $table->json('sharing')->nullable();
            $table->json('associated_apps')->nullable();
            $table->json('fields')->nullable();
            $table->unsignedInteger('max_distance')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('templates');
    }
};
