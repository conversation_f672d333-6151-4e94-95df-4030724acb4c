<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('field_pass', function (Blueprint $table) {
            $table->id();
            $table->foreignUuid('field_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('pass_id')->constrained()->cascadeOnDelete();
            $table->text('value')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('field_pass');
    }
};
