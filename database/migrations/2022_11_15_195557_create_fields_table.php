<?php

use App\Enums\FieldTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('fields', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('project_id')->constrained()->cascadeOnDelete();
            $table->string('title');
            $table->string('key');
            $table->enum('type', FieldTypeEnum::values());
            $table->json('enum')->nullable();
            $table->boolean('required');
            $table->boolean('show_in_list')->default(true);
            $table->text('default_value')->nullable();
            $table->timestamps();
        });

        Schema::table('projects', function (Blueprint $table) {
            $table->foreignUuid('external_id_field_id')->nullable()->constrained('fields')->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('projects', function (Blueprint $table) {
            $table->dropForeign(['external_id_field_id']);
            $table->dropColumn('external_id_field_id');
        });

        Schema::dropIfExists('fields');
    }
};
