<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pass_events', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('pass_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignUuid('project_id')->constrained()->cascadeOnDelete();
            $table->enum('type', \App\Enums\PassEventTypeEnum::values())->index();
            $table->timestamps();

            $table->index(['project_id', 'type', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pass_events');
    }
};
