<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('beaconables', function (Blueprint $table) {
            $table->id();
            $table->foreignUuid('beacon_id')->constrained()->cascadeOnDelete();
            $table->uuidMorphs('beaconable');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('beaconables');
    }
};
