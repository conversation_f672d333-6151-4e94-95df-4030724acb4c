<div>
    <h3 class="text-lg font-medium leading-6 text-gray-900">Last 30 days</h3>
    <dl class="mt-5 grid grid-cols-1 divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow md:grid-cols-3 md:divide-y-0 md:divide-x">
        <div class="px-4 py-5 sm:p-6">
            <dt class="text-base font-normal text-gray-900">Installs</dt>
            <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                    {{ number_format($stats['installs']['last30'], 0, ',', ' ') }}
                    <span class="ml-2 text-sm font-medium text-gray-500">compared to {{ number_format($stats['installs']['previous30'], 0, ',', ' ') }}</span>
                </div>

                @if(isset($stats['installs']['delta']))
                    @if($stats['installs']['delta'] > 0)
                        <div class="inline-flex items-baseline px-2.5 py-0.5 rounded-full text-sm font-medium bg-green-100 text-green-800 md:mt-2 lg:mt-0">
                            <!-- Heroicon name: mini/arrow-up -->
                            <svg class="-ml-1 mr-0.5 h-5 w-5 flex-shrink-0 self-center text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04l-3.96-4.158V16.25A.75.75 0 0110 17z" clip-rule="evenodd" />
                            </svg>
                            <span class="sr-only"> Increased </span>
                            {{ number_format($stats['installs']['delta'], 2, ',', ' ') }}%
                        </div>
                    @else
                        <div class="inline-flex items-baseline px-2.5 py-0.5 rounded-full text-sm font-medium bg-red-100 text-red-800 md:mt-2 lg:mt-0">
                            <!-- Heroicon name: mini/arrow-up -->
                            <svg class="-ml-1 mr-0.5 h-5 w-5 flex-shrink-0 self-center text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M10 3a.75.75 0 01.75.75v10.638l3.96-4.158a.75.75 0 111.08 1.04l-5.25 5.5a.75.75 0 01-1.08 0l-5.25-5.5a.75.75 0 111.08-1.04l3.96 4.158V3.75A.75.75 0 0110 3z" clip-rule="evenodd" />
                            </svg>

                            <span class="sr-only"> Decreased </span>
                            {{ number_format($stats['installs']['delta'], 2, ',', ' ') }}%
                        </div>
                    @endif
                @endif
            </dd>
        </div>

        <div class="px-4 py-5 sm:p-6">
            <dt class="text-base font-normal text-gray-900">Uninstalls</dt>
            <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                    {{ number_format($stats['uninstalls']['last30'], 0, ',', ' ') }}
                    <span class="ml-2 text-sm font-medium text-gray-500">compared to {{ number_format($stats['uninstalls']['previous30'], 0, ',', ' ') }}</span>
                </div>

                @if(isset($stats['uninstalls']['delta']))
                    @if($stats['uninstalls']['delta'] > 0)
                        <div class="inline-flex items-baseline px-2.5 py-0.5 rounded-full text-sm font-medium bg-green-100 text-green-800 md:mt-2 lg:mt-0">
                            <!-- Heroicon name: mini/arrow-up -->
                            <svg class="-ml-1 mr-0.5 h-5 w-5 flex-shrink-0 self-center text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04l-3.96-4.158V16.25A.75.75 0 0110 17z" clip-rule="evenodd" />
                            </svg>
                            <span class="sr-only"> Increased </span>
                            {{ number_format($stats['uninstalls']['delta'], 2, ',', ' ') }}%
                        </div>
                    @else
                        <div class="inline-flex items-baseline px-2.5 py-0.5 rounded-full text-sm font-medium bg-red-100 text-red-800 md:mt-2 lg:mt-0">
                            <!-- Heroicon name: mini/arrow-up -->
                            <svg class="-ml-1 mr-0.5 h-5 w-5 flex-shrink-0 self-center text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M10 3a.75.75 0 01.75.75v10.638l3.96-4.158a.75.75 0 111.08 1.04l-5.25 5.5a.75.75 0 01-1.08 0l-5.25-5.5a.75.75 0 111.08-1.04l3.96 4.158V3.75A.75.75 0 0110 3z" clip-rule="evenodd" />
                            </svg>

                            <span class="sr-only"> Decreased </span>
                            {{ number_format($stats['uninstalls']['delta'], 2, ',', ' ') }}%
                        </div>
                    @endif
                @endif
            </dd>
        </div>

        <div class="px-4 py-5 sm:p-6">
            <dt class="text-base font-normal text-gray-900">Active Registrations</dt>
            <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                    {{ number_format($stats['registrations']['last30'], 0, ',', ' ') }}
                    <span class="ml-2 text-sm font-medium text-gray-500">compared to {{ number_format($stats['registrations']['previous30'], 0, ',', ' ') }}</span>
                </div>

                @if(isset($stats['registrations']['delta']))
                    <div class="inline-flex items-baseline px-2.5 py-0.5 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800 md:mt-2 lg:mt-0">
                        {{ number_format($stats['registrations']['delta'], 2, ',', ' ') }}%
                    </div>
                @endif
            </dd>
        </div>
    </dl>
</div>
