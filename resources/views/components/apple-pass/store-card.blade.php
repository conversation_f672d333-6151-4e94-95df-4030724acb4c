@php
    $backgroundColor = $template->colors['background'];
    $labelColor = $template->colors['label'];
@endphp

<div style="position:relative; top: 0; left: 0; border-radius: 10px; width: 288px; height: 400px; color: {{ $labelColor }}; background-color: {{ $backgroundColor }}">
    <x-apple-pass.store-card.sections.header :template="$template" />
    <x-apple-pass.store-card.sections.primary :template="$template" />
    <x-apple-pass.store-card.sections.secondary :template="$template" />
    <x-apple-pass.store-card.sections.auxiliary :template="$template" />
    <x-apple-pass.barcode :template="$template" />
</div>
