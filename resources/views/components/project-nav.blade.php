@props(['project'])

@if(isset($project) && $project->id)

    <div class="flex justify-between">
        <div class="space-x-8 -my-px flex">
            <div class="relative flex items-center">
                <x-jet-dropdown align="left" width="60">
                    <x-slot name="trigger">
                        <span class="inline-flex rounded-md">
                            <button type="button" class="inline-flex items-center border border-transparent rounded-md font-semibold text-xl text-gray-800 leading-tight bg-white focus:outline-none transition">
                                <span class="flex-1">{{ $project->title }}</span>

                                <svg class="flex-1 ml-2 -mr-0.5 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 3a1 1 0 01.707.293l3 3a1 1 0 01-1.414 1.414L10 5.414 7.707 7.707a1 1 0 01-1.414-1.414l3-3A1 1 0 0110 3zm-3.707 9.293a1 1 0 011.414 0L10 14.586l2.293-2.293a1 1 0 011.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </span>
                    </x-slot>

                    <x-slot name="content">
                        <div class="w-60">
                            <div class="block px-4 py-2 text-xs text-gray-400">
                                {{ __('Switch Project') }}
                            </div>

                           @foreach(auth()->user()->projects as $p)
                                <x-jet-dropdown-link href="{{ route('projects.show', $p) }}">
                                    <div class="flex items-center">
                                        @if($p->id == $project->id)
                                            <svg class="mr-2 h-5 w-5 text-green-400" fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" stroke="currentColor" viewBox="0 0 24 24"><path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                                        @endif
                                        <div class="truncate">{{ $p->title }}</div>
                                    </div>
                                </x-jet-dropdown-link>
                            @endforeach

                        </div>
                    </x-slot>
                </x-jet-dropdown>
            </div>

            <x-jet-nav-link href="{{ route('projects.show', $project) }}" :active="request()->routeIs('projects.show', $project->id)" :sub="true">
                {{ __('Dashboard') }}
            </x-jet-nav-link>
            <x-jet-nav-link href="{{ route('projects.templates.index', $project) }}" :active="request()->routeIs('projects.templates.*', 'templates.*')" :sub="true">
                {{ __('Designs') }}
            </x-jet-nav-link>
            <x-jet-nav-link href="{{ route('projects.passes.index', $project) }}" :active="request()->routeIs('passes.*', 'projects.passes.*')" :sub="true">
                {{ __('Passes') }}
            </x-jet-nav-link>
        </div>

        <div class="relative flex items-center">
            <x-jet-dropdown align="right" width="60">
                <x-slot name="trigger">
                    <button type="button" class="inline-flex items-center border border-transparent rounded-md font-semibold text-xl text-gray-800 leading-tight bg-white focus:outline-none transition">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </button>
                </x-slot>

                <x-slot name="content">
                    <div class="w-60">
                        <div class="block px-4 py-2 text-xs text-gray-400">
                            {{ __('Actions') }}
                        </div>

                        <x-jet-dropdown-link href="{{ route('projects.edit', $project) }}">
                            <div class="flex items-center">
                                <div class="truncate">{{ __('Configure project') }}</div>
                            </div>
                        </x-jet-dropdown-link>

                    </div>
                </x-slot>
            </x-jet-dropdown>
        </div>
    </div>

@endif
