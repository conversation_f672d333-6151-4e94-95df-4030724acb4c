<x-slot name="header">
    <x-project-nav :project="$project"/>
</x-slot>

<x-main-container>

    <x-jet-form-section submit="save">

        <x-slot name="description">
            <x-apple-pass.store-card :template="$template" />
        </x-slot>

        <x-slot name="form">

            <div class="col-span-6 sm:col-span-4">
                <x-jet-label for="template.title" value="{{ __('Title') }}" />
                <x-jet-input id="template.title" wire:model="template.title" type="text" class="mt-1 block w-full" autocomplete="off" />
                <x-jet-input-error for="template.title" class="mt-2" />
            </div>

            <div class="col-span-6 sm:col-span-4">
                <x-jet-label for="template.organization_name" value="{{ __('Organization Name') }}" />
                <x-jet-input id="template.organization_name" wire:model="template.organization_name" type="text" class="mt-1 block w-full" autocomplete="off" />
                <x-jet-input-error for="template.organization_name" class="mt-2" />
            </div>

            <div class="col-span-6 sm:col-span-4">
                <x-jet-label for="template.description" value="{{ __('Description') }}" />
                <x-jet-input id="template.description" wire:model="template.description" type="text" class="mt-1 block w-full" autocomplete="off" />
                <x-jet-input-error for="template.description" class="mt-2" />
            </div>

            <div class="col-span-4 grid grid-cols-2 gap-6">

                <div>
                    <x-jet-label for="template.colors.background" value="{{ __('Background Color') }}" />
                    <x-jet-input id="template.colors.background" wire:model="template.colors.background" type="color" class="mt-1 block w-full" autocomplete="off" />
                    <x-jet-input-error for="template.colors.background" class="mt-2" />
                </div>

                <div>
                    <x-jet-label for="template.colors.label" value="{{ __('Label Color') }}" />
                    <x-jet-input id="template.colors.label" wire:model="template.colors.label" type="color" class="mt-1 block w-full" autocomplete="off" />
                    <x-jet-input-error for="template.colors.label" class="mt-2" />
                </div>

            </div>

            <div class="col-span-6 sm:col-span-4">
                <x-jet-label for="template.barcode.alt_text" value="{{ __('Barcode Alt Text') }}" />
                <x-jet-input id="template.barcode.alt_text" wire:model="template.barcode.alt_text" type="text" class="mt-1 block w-full" autocomplete="off" />
                <x-jet-input-error for="template.barcode.alt_text" class="mt-2" />
            </div>

        </x-slot>

        <x-slot name="actions">
            <x-jet-button>
                {{ __('Save') }}
            </x-jet-button>
        </x-slot>
    </x-jet-form-section>

    </div>

</x-main-container>
