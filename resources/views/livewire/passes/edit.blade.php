<x-slot name="header">
    <x-project-nav :project="$project"/>
</x-slot>

<x-main-container>

    @if($pass->id)

        <x-jet-form-section submit="">
            <x-slot name="title">
                {{ __('Pass Information') }}
            </x-slot>

            <x-slot name="description">
                {{ __('Issued pass general information.') }}
            </x-slot>

            <x-slot name="form">
                <div class="col-span-6 sm:col-span-4">
                    <x-jet-label for="pass.id" value="{{ __('Serial Number') }}" />
                    <x-jet-input id="pass.id" disabled="disabled" type="text" class="mt-1 block w-full" wire:model="pass.id" autocomplete="off" />
                </div>
                <div class="col-span-6 sm:col-span-4" x-data>
                    <x-jet-label for="pass.id" value="{{ __('Pass Installation Link') }}" />
                    <div class="flex items-center mt-1">
                        <button type="button" @click="copyTextToClipboard('{{ $pass->install_url }}');" wire:click="$emit('notify');" class="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-3 py-2 text-sm font-medium leading-4 text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                            Copy URL
                            <!-- Heroicon name: mini/envelope -->
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="ml-2 -mr-1 h-5 w-5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M13.19 8.688a4.5 4.5 0 011.242 7.244l-4.5 4.5a4.5 4.5 0 01-6.364-6.364l1.757-1.757m13.35-.622l1.757-1.757a4.5 4.5 0 00-6.364-6.364l-4.5 4.5a4.5 4.5 0 001.242 7.244" />
                            </svg>
                        </button>
                        <x-jet-action-message class="ml-3" on="notify">
                            {{ __('Copied.') }}
                        </x-jet-action-message>
                    </div>
                </div>
            </x-slot>
        </x-jet-form-section>

    @endif

    <x-jet-form-section class="mt-6" submit="save">
        <x-slot name="title">
            {{ __('Pass Details') }}
        </x-slot>

        <x-slot name="description">
            {{ __('Update fields.') }}
        </x-slot>

        <x-slot name="form">

            <div class="col-span-6 sm:col-span-4">
                <x-jet-label for="pass.template_id" class="font-bold" value="{{ __('Template') }} *" />
                <x-input.select id="pass.template_id" class="mt-1 block w-full" required="required" name="pass.template_id" wire:model.defer="pass.template_id">
                    <option value="">(Not Selected)</option>
                    @foreach($project->templates as $template)
                        <option value="{{ $template->id }}">{{ $template->title }}</option>
                    @endforeach
                </x-input.select>
                <x-jet-input-error for="pass.template_id" class="mt-2" />
            </div>

            @foreach($fields as $field)
                <div class="col-span-6 sm:col-span-4">
                    @if($field['type'] == \App\Enums\FieldTypeEnum::ENUM->value)
                        <x-jet-label for="fields.{{ $loop->index }}" :class="$field['required'] ? 'font-bold' : ''" value="{{ $field['title'] . ($field['required'] ? ' *' : '') }}" />
                        <x-input.select id="fields.{{ $loop->index }}" class="mt-1 block w-full" :required="$field['required']" name="fields.{{ $loop->index }}.value" wire:model.defer="fields.{{ $loop->index }}.value">
                            <option value="">(Not Selected)</option>
                            @foreach($field['enum'] ?? [] as $value)
                                <option value="{{ $value }}">{{ $value }}</option>
                            @endforeach
                        </x-input.select>
                    @elseif($field['type'] == \App\Enums\FieldTypeEnum::BOOLEAN->value)
                        <div class="relative flex items-start">
                            <div class="flex h-5 items-center">
                                <input id="fields.{{ $loop->index }}" wire:model="fields.{{ $loop->index }}.value" aria-describedby="comments-description" name="fields.{{ $loop->index }}.value" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="field.default_value" class="font-medium text-gray-700">{{ $field['title'] }}</label>
                            </div>
                        </div>
                    @else
                        <x-jet-label for="fields.{{ $loop->index }}.value" :class="$field['required'] ? 'font-bold' : ''" value="{{ $field['title'] . ($field['required'] ? ' *' : '') }}" />
                        <x-jet-input id="fields.{{ $loop->index }}.value" type="text" :required="$field['required']" class="mt-1 block w-full" wire:model.defer="fields.{{ $loop->index }}.value" autocomplete="off" />
                    @endif
                    <x-jet-input-error for="fields.{{ $loop->index }}.value" class="mt-2" />
                </div>
            @endforeach

            <div class="col-span-6 sm:col-span-4">
                <x-jet-label for="pass.external_id" value="{{ __('External ID') }}" />
                <x-jet-input id="pass.external_id" type="text" class="mt-1 block w-full" wire:model="pass.external_id" autocomplete="off" />
                <x-jet-input-error for="pass.external_id" class="mt-2" />
            </div>
        </x-slot>

        <x-slot name="actions">
            <x-jet-action-message class="mr-3" on="saved">
                {{ __('Saved.') }}
            </x-jet-action-message>

            <x-jet-button wire:loading.attr="disabled">
                {{ __('Save') }}
            </x-jet-button>
        </x-slot>
    </x-jet-form-section>

    @push('scripts')
        <script>
            function fallbackCopyTextToClipboard(text) {
                var textArea = document.createElement("textarea");
                textArea.value = text;

                textArea.style.top = "0";
                textArea.style.left = "0";
                textArea.style.position = "fixed";

                document.body.appendChild(textArea);

                textArea.focus();
                textArea.select();

                try {
                    var successful = document.execCommand('copy');
                    var msg = successful ? 'successful' : 'unsuccessful';
                    console.log('Fallback: Copying text command was ' + msg);
                } catch (err) {
                    console.error('Fallback: Oops, unable to copy', err);
                }

                document.body.removeChild(textArea);
            }
            function copyTextToClipboard(text) {
                if (!navigator.clipboard) {
                    fallbackCopyTextToClipboard(text);
                    return;
                }
                navigator.clipboard.writeText(text).then(function() {
                    console.log('Async: Copying to clipboard was successful!');
                }, function(err) {
                    console.error('Async: Could not copy text: ', err);
                });
            }
        </script>
    @endpush

</x-main-container>
