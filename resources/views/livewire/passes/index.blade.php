<x-slot name="header">
    <x-project-nav :project="$project"/>
</x-slot>

<x-main-container>
    <div>
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-xl font-semibold text-gray-900">Passes</h1>
                <p class="mt-2 text-sm text-gray-700">A list of issued passes.</p>
            </div>
            <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
                <a href="{{ route('projects.passes.create', $project->id) }}" class="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto">Issue pass</a>
            </div>
        </div>
        <div class="mt-8 flex flex-col relative">
            <div class="w-full">
                <div class=" min-w-full py-2">
                    <div class="overflow-x-scroll shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                        <table class="min-w-full divide-y divide-gray-300" style="width:100%">
                            <thead class="bg-gray-50">
                            <tr>
                                @foreach($project->fields()->where('show_in_list', true)->get() as $field)
                                    <th scope="col"
                                        @if ($loop->first)
                                            class="py-3.5 text-left text-sm font-semibold text-gray-900 sm:pl-6 truncate"
                                        @else
                                            class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 truncate"
                                        @endif
                                    >{{ $field->title }}</th>
                                @endforeach
                                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 truncate">
                                    Last updated
                                </th>
                                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                                    <span class="sr-only">Edit</span>
                                </th>
                            </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-white">
                            @foreach($passes as $pass)
                                <tr>
                                    @foreach($project->fields()->where('show_in_list', true)->get() as $field)
                                        @php
                                            $field = $pass->fields->where('id', $field->id)->first() ?? $field;
                                            $fieldValue = $field->pivot->value ?? $field->default_value;
                                        @endphp
                                        <td
                                            @if ($loop->first)
                                                class="whitespace-nowrap py-4 pl-4 pr-3 text-sm text-gray-900 sm:pl-6 truncate"
                                            @else
                                                class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 truncate"
                                            @endif
                                        >
                                            @if($field->type == \App\Enums\FieldTypeEnum::BOOLEAN)
                                                @if($fieldValue == true)
                                                    <span class="inline-flex items-center text-xs font-medium">
                                                      <svg class="mr-1.5 h-5 w-5 flex-shrink-0 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
                                                      </svg>
                                                      Yes
                                                    </span>
                                                @else
                                                    <span class="inline-flex items-center text-xs font-medium">
                                                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="mr-1.5 h-5 w-5 flex-shrink-0 text-red-400">
                                                          <path fill-rule="evenodd" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zm3 10.5a.75.75 0 000-1.5H9a.75.75 0 000 1.5h6z" clip-rule="evenodd" />
                                                      </svg>
                                                      No
                                                    </span>
                                                @endif
                                            @else
                                                @if($fieldValue)
                                                    {{ $fieldValue }}
                                                @else
                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6">
                                                        <path fill-rule="evenodd" d="M3.75 12a.75.75 0 01.75-.75h15a.75.75 0 010 1.5h-15a.75.75 0 01-.75-.75z" clip-rule="evenodd" />
                                                    </svg>
                                                @endif
                                            @endif
                                        </td>
                                    @endforeach
                                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 truncate">
                                        {{ $pass->updated_at->diffForHumans() }}
                                    </td>
                                    <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                                        <a href="{{ route('passes.edit', $pass) }}" class="text-indigo-600 hover:text-indigo-900">Edit<span class="sr-only">, Lindsay Walton</span></a>
                                    </td>
                                </tr>
                            @endforeach

                            <!-- More people... -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="mt-3">
                {{ $passes->links() }}
            </div>
        </div>
    </div>
</x-main-container>
