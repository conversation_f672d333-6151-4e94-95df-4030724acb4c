@if($project && $project->id)

    <x-slot name="header">
        <x-project-nav :project="$project"/>
    </x-slot>

@endif

<x-main-container>

    <x-jet-form-section submit="saveProject">
        <x-slot name="title">
            {{ __('General Information') }}
        </x-slot>

        <x-slot name="description">
            {{ __('Update your account\'s profile information and email address.') }}
        </x-slot>

        <x-slot name="form">

            <div class="col-span-6 sm:col-span-4">
                <x-jet-label for="project.title" value="{{ __('Title') }}" />
                <x-jet-input id="project.title" type="text" class="mt-1 block w-full" wire:model="project.title" autocomplete="off" />
                <x-jet-input-error for="project.title" class="mt-2" />
            </div>

            <div class="col-span-6 sm:col-span-4">
                <x-jet-label for="project.title" value="{{ __('Project type') }}" />
                <x-input.select id="project.title" name="project.type" class="mt-1 block w-full" wire:model="project.type">
                    @foreach(\App\Enums\ProjectTypeEnum::values() as $projectType)
                        <option value="{{ $projectType }}">{{ __("frontend.{$projectType}") }}</option>
                    @endforeach
                </x-input.select>
                <x-jet-input-error for="project.title" class="mt-2" />
            </div>

            @if($project->id)
                <div class="col-span-6 sm:col-span-4">
                    <x-jet-label for="project.external_id_field_id" value="{{ __('External ID field') }}" />
                    <x-input.select id="project.external_id_field_id" name="project.external_id_field_id" class="mt-1 block w-full" wire:model="project.external_id_field_id">
                        <option value="">(Not Selected)</option>
                        @foreach($project->fields as $field)
                            <option value="{{ $field->id }}">{{ $field->title }}</option>
                        @endforeach
                    </x-input.select>
                    <x-jet-input-error for="project.external_id_field_id" class="mt-2" />
                </div>
            @endif
        </x-slot>

        <x-slot name="actions">
            <x-jet-action-message class="mr-3" on="projectSaved">
                {{ __('Saved.') }}
            </x-jet-action-message>

            <x-jet-button wire:loading.attr="disabled">
                {{ __('Save') }}
            </x-jet-button>
        </x-slot>
    </x-jet-form-section>

    @if($project->id)

        <x-jet-section-border />

        <x-jet-form-section submit="saveCertificate" x-data="{}" >
            <x-slot name="title">
                {{ __('Certificates') }}
            </x-slot>

            <x-slot name="description">
                {{ __('You must upload your own certificate to be able to commercially issue passes. Apple requires you to have an Apple Developer Account in order for you to create your own Apple Wallet Certificates.') }}
            </x-slot>

            <x-slot name="form">
                <div class="col-span-12">
                    <div class="max-w-xl text-sm text-gray-600">
                        {{ __('It is not possible to change the certificate on a live project; the same Pass Type Identifier needs to be used upon renewal.') }}
                    </div>

                    <div>
                        <input type="file" class="hidden" accept=".cer"
                               wire:model="uploadedCertificate"
                               x-ref="certificate" />

                        <x-jet-input-error for="uploadedCertificate" class="mt-2" />

                    </div>

                    @if($project->certificate)

                        <div class="mt-5 space-y-6">
                            <div class="flex items-center">
                                <div>
                                    <svg fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor" class="w-8 h-8 text-gray-500">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                                    </svg>
                                </div>

                                <div class="ml-3">
                                    <div class="text-sm text-gray-600">
                                        {{ $project->certificate?->pass_type_id }}
                                    </div>

                                    <div>
                                        <div class="text-xs text-gray-500">

                                            @php
                                                $validClass = "text-{$project->certificate->warning_level->value}-500 ";
                                            @endphp

                                            {{ $project->certificate->issued_for }},

                                            <span class="{{ $validClass }}">{{ __('Expires') }} <span class="font-semibold">{{ $project->certificate?->expires_in }}</span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </x-slot>
            <x-slot name="actions">

                <x-jet-action-message on="certificateSaved">
                    {{ __('Saved.') }}
                </x-jet-action-message>

                <x-jet-secondary-button class="ml-3" type="button" x-on:click.prevent="$refs.certificate.click()" wire:target="certificateUpdated">
                    {{ __('Upload New Certificate') }}
                </x-jet-secondary-button>

                <x-jet-secondary-button type="button" class="ml-3" wire:click="downloadCsr">
                    {{ __('CSR') }}
                </x-jet-secondary-button>

                @if($uploadedCertificate)
                    <x-jet-button class="ml-3" >
                        {{ __('Save') }}
                    </x-jet-button>
                @endif
            </x-slot>
        </x-jet-form-section>

        <x-jet-section-border />

        <x-jet-action-section submit="saveProject">
            <x-slot name="title">
                {{ __('Fields') }}
            </x-slot>

            <x-slot name="description">
                {{ __('Change fields') }}
            </x-slot>

            <x-slot name="content">
                <div class="flex flex-col items-end">
                    <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
                        <a href="{{ route('fields.create', $project->id) }}" class="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto">Add field</a>
                    </div>
                </div>

                @if($project->fields->count() > 0)

                    <div class="-mx-6 mt-6 overflow-hidden bg-white">
                        <ul role="list" class="divide-y divide-gray-100">
                            @foreach($project->fields as $field)
                                <li>
                                    <a href="{{ route('fields.edit', $field->id) }}" class="block hover:bg-gray-50">
                                        <div class="flex items-center px-4 py-4 sm:px-6">
                                            <div class="min-w-0 flex-1 md:grid-cols-2">
                                                <div>
                                                    <p class="truncate text-sm font-medium text-gray-600">{{ $field->title }}</p>
                                                    <p class="mt-2 flex items-center text-sm text-gray-500">
                                                        @if($field->required)
                                                            <span class="inline-flex mr-2 items-center rounded-full bg-orange-100 px-2.5 py-0.5 text-xs font-medium text-orange-800">{{ __('Required') }}</span>
                                                        @endif
                                                        <span class="inline-flex mr-2 items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">{{ __(\Str::ucfirst($field->type->value)) }}</span>
                                                        @if($field->default_value)
                                                            <span class="inline-flex mr-2 items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">Default value: {{ $field->default_value }}</span>
                                                        @endif
                                                        @if($field->show_in_list)
                                                            <span class="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">Display on Passes page</span>
                                                        @endif
                                                    </p>
                                                </div>
                                            </div>
                                            <div>
                                                <!-- Heroicon name: mini/chevron-right -->
                                                <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                    <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                        </div>
                                    </a>
                                </li>
                            @endforeach
                        </ul>
                    </div>

                @endif

            </x-slot>

        </x-jet-action-section>

    @endif

</x-main-container>
