<x-slot name="header">
    <x-project-nav :project="$project"/>
</x-slot>

<x-main-container>

    <nav class="flex" aria-label="Breadcrumb">
        <ol role="list" class="flex items-center space-x-4">
            <li>
                <div>
                    <a href="{{ route('projects.index') }}" class="text-gray-400 hover:text-gray-500">
                        <svg class="h-5 w-5 flex-shrink-0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M9.293 2.293a1 1 0 011.414 0l7 7A1 1 0 0117 11h-1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-3a1 1 0 00-1-1H9a1 1 0 00-1 1v3a1 1 0 01-1 1H5a1 1 0 01-1-1v-6H3a1 1 0 01-.707-1.707l7-7z" clip-rule="evenodd" />
                        </svg>
                        <span class="sr-only">Home</span>
                    </a>
                </div>
            </li>

            <li>
                <div class="flex items-center">
                    <svg class="h-5 w-5 flex-shrink-0 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                        <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                    </svg>
                    <a href="{{ route('projects.show', $project->id) }}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">{{ $project->title }}</a>
                </div>
            </li>

            <li>
                <div class="flex items-center">
                    <svg class="h-5 w-5 flex-shrink-0 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                        <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                    </svg>
                    <a href="{{ route('projects.edit', $project->id) }}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">Settings</a>
                </div>
            </li>

            <li>
                <div class="flex items-center">
                    <svg class="h-5 w-5 flex-shrink-0 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                        <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                    </svg>
                    <span class="ml-4 text-sm font-medium text-gray-500" aria-current="page">{{ $field->id ? __('Edit field') . " \"{$field->title}\"" : __('Add new field') }}</span>
                </div>
            </li>
        </ol>
    </nav>



    <x-jet-form-section submit="save" class="mt-10">
        <x-slot name="title">
            {{ __('Field Information') }}
        </x-slot>

        <x-slot name="description">
            {{ __('Update your account\'s profile information and email address.') }}
        </x-slot>

        <x-slot name="form">

            @if($field->id)
                <div class="col-span-6 sm:col-span-4">
                    <x-jet-label for="field.id" value="{{ __('Field ID') }}" />
                    <x-jet-input id="field.id" disabled="disabled" type="text" class="mt-1 block w-full" wire:model="field.id" autocomplete="off" />
                </div>
            @endif

            <div class="col-span-6 sm:col-span-4">
                <x-jet-label for="field.title" value="{{ __('Title') }} *" />
                <x-jet-input id="field.title" type="text" class="mt-1 block w-full" wire:model="field.title" autocomplete="off" />
                <x-jet-input-error for="field.title" class="mt-2" />
            </div>

            <div class="col-span-6 sm:col-span-4">
                <x-jet-label for="field.key" value="{{ __('Key') }} *" />
                <x-jet-input id="field.key" type="text" class="mt-1 block w-full" wire:model="field.key" autocomplete="off" />
                @if($field->key)
                    <p class="mt-2 text-sm text-gray-500">Template usage: <strong>{{ sprintf('${%s}', $field->key) }}</strong></p>
                @endif
                <x-jet-input-error for="field.key" class="mt-2" />
            </div>

            <div class="col-span-6 sm:col-span-4">
                <x-jet-label for="field.type" value="{{ __('Field type') }} *" />
                <x-input.select id="field.type" name="field.type" class="mt-1 block w-full" wire:model="field.type">
                    @foreach(\App\Enums\FieldTypeEnum::values() as $fieldType)
                        <option value="{{ $fieldType }}">{{ __(\Str::ucfirst($fieldType)) }}</option>
                    @endforeach
                </x-input.select>
                <x-jet-input-error for="field.type" class="mt-2" />
            </div>

            @if($field->type == \App\Enums\FieldTypeEnum::ENUM)
                <div class="col-span-6 sm:col-span-4">
                    <x-jet-label for="field.enum" value="{{ __('Allowed values') }} *" />
                    <livewire:fields.enum-input :enum="$field->enum" />
                    <x-jet-input-error for="field.enum" class="mt-2" />
                </div>
            @endif

            <div class="col-span-6 sm:col-span-4">
                @if($field->type == \App\Enums\FieldTypeEnum::ENUM)
                    <x-jet-label for="field.default_value" value="{{ __('Default value') }}" />
                    <x-input.select id="field.default_value" name="field.default_value" class="mt-1 block w-full" wire:model="field.default_value">
                        <option value="">(Not Selected)</option>
                        @foreach($field->enum ?? [] as $value)
                            <option value="{{ $value }}">{{ $value }}</option>
                        @endforeach
                    </x-input.select>
                @elseif($field->type == \App\Enums\FieldTypeEnum::BOOLEAN)
                    <div class="relative flex items-start">
                        <div class="flex h-5 items-center">
                            <input id="field.default_value" wire:model="field.default_value" aria-describedby="comments-description" name="field.default_value" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                        </div>
                        <div class="ml-3 text-sm">
                            <label for="field.default_value" class="font-medium text-gray-700">{{ __('Checked by default') }}</label>
                        </div>
                    </div>
                @else
                    <x-jet-label for="field.default_value" value="{{ __('Default value') }}" />
                    <x-jet-input id="field.default_value" type="text" class="mt-1 block w-full" wire:model="field.default_value" autocomplete="off" />
                @endif
                <x-jet-input-error for="field.default_value" class="mt-2" />
            </div>

            <div class="col-span-6 sm:col-span-4">
                <div class="relative flex items-start">
                    <div class="flex h-5 items-center">
                        <input id="field.required" wire:model="field.required" aria-describedby="comments-description" name="field.required" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                    </div>
                    <div class="ml-3 text-sm">
                        <label for="field.required" class="font-medium text-gray-700">Required field</label>
                    </div>
                </div>
                <x-jet-input-error for="field.required" class="mt-2" />
            </div>

            <div class="col-span-6 sm:col-span-4">
                <div class="relative flex items-start">
                    <div class="flex h-5 items-center">
                        <input id="field.show_in_list" wire:model="field.show_in_list" aria-describedby="comments-description" name="field.show_in_list" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                    </div>
                    <div class="ml-3 text-sm">
                        <label for="field.show_in_list" class="font-medium text-gray-700">Display on <a href="{{ route('projects.passes.index', $project->id) }}" class="border-indigo-600 text-indigo-600 underline">Passes page</a></label>
                    </div>
                </div>
                <x-jet-input-error for="field.show_in_list" class="mt-2" />
            </div>

        </x-slot>

        <x-slot name="actions">
            <x-jet-action-message class="mr-3" on="saved">
                {{ __('Saved.') }}
            </x-jet-action-message>

            <x-jet-button wire:loading.attr="disabled">
                {{ __('Save') }}
            </x-jet-button>
        </x-slot>
    </x-jet-form-section>

</x-main-container>
