<div>
    <x-jet-input id="newValue" type="text" class="mt-1 block w-full" placeholder="{{ __('Type and press Enter') }}" wire:model="newValue" autocomplete="off" wire:keydown.enter.prevent="add" />
    <x-jet-input-error for="newValue" class="mt-2" />
    <div class="mt-1">
        @foreach($enum as $index => $value)
            <span class="inline-flex items-center rounded-full bg-indigo-100 py-0.5 pl-2 pr-0.5 text-xs font-medium text-indigo-700">
                {{ $value }}
                <button wire:click="remove({{ $index }})" type="button" class="ml-0.5 inline-flex h-4 w-4 flex-shrink-0 items-center justify-center rounded-full text-indigo-400 hover:bg-indigo-200 hover:text-indigo-500 focus:bg-indigo-500 focus:text-white focus:outline-none">
                    <span class="sr-only">Remove small option</span>
                    <svg class="h-2 w-2" stroke="currentColor" fill="none" viewBox="0 0 8 8">
                        <path stroke-linecap="round" stroke-width="1.5" d="M1 1l6 6m0-6L1 7" />
                    </svg>
                </button>
            </span>
        @endforeach
    </div>
</div>
