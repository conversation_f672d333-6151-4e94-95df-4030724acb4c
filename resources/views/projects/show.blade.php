<x-app-layout>

    <x-slot name="header">
        <x-project-nav :project="$project"/>
    </x-slot>

    <x-main-container>
        <x-dashboard-stats :stats="$stats" />
    </x-main-container>

    <x-main-container>
        <dl class="mt-5 rounded-lg bg-white shadow px-4 py-5 sm:p-6">
            <canvas id="myChart"></canvas>
        </dl>
    </x-main-container>

    @push('scripts')
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

        <script>
            const ctx = document.getElementById('myChart');

            const labels = [65, 59, 80, 81, 56, 55, 40];

            const data = {
                labels: labels,
                datasets: [{
                    label: '{{ __('Installs') }}',
                    data: [65, 59, 80, 81, 56, 55, 40],
                    fill: false,
                    borderColor: '#2dd4bf',
                    tension: 0.2
                }, {
                    label: '{{ __('Uninstalls') }}',
                    data: [56, 55, 40, 65, 59, 80, 81],
                    fill: false,
                    borderColor: '#d8b4fe',
                    tension: 0.2
                }]
            };

            const config = {
                type: 'line',
                data: data,
                options: {
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {

                            }
                        }
                    }
                }
            };

            new Chart(ctx, config);
        </script>
    @endpush

</x-app-layout>
