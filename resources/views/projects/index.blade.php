<x-app-layout>

    <x-main-container>
        <div class="bg-white shadow sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium leading-6 text-gray-900">Google Wallet Support</h3>
                <div class="mt-2 max-w-xl text-sm text-gray-500">
                    <p>Google Wallet passes will be implemented soon.</p>
                </div>
                <div class="mt-3 text-sm">
                    <a href="#" class="font-medium text-indigo-600 hover:text-indigo-500">
                        Get notified
                        <span aria-hidden="true"> &rarr;</span>
                    </a>
                </div>
            </div>
        </div>
    </x-main-container>

    <x-main-container>


        <ul role="list" class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">

            @foreach($projects as $project)

                <li class="col-span-1 divide-y divide-gray-200 rounded-lg bg-white shadow cursor-pointer" onclick="location.href = '{{ route('projects.show', $project) }}';">
                    <div class="px-4 py-5 sm:px-6">
                        <div>
                          <span class="rounded-lg inline-flex p-3 bg-pink-50 text-pink-400 ring-4 ring-white">
                            <!-- Heroicon name: outline/clock -->
                           <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6">
  <path d="M5.223 2.25c-.497 0-.974.198-1.325.55l-1.3 1.298A3.75 3.75 0 007.5 9.75c.627.47 1.406.75 2.25.75.844 0 1.624-.28 2.25-.75.626.47 1.406.75 2.25.75.844 0 1.623-.28 2.25-.75a3.75 3.75 0 004.902-5.652l-1.3-1.299a1.875 1.875 0 00-1.325-.549H5.223z" />
  <path fill-rule="evenodd" d="M3 20.25v-8.755c1.42.674 3.08.673 4.5 0A5.234 5.234 0 009.75 12c.804 0 1.568-.182 2.25-.506a5.234 5.234 0 002.25.506c.804 0 1.567-.182 2.25-.506 1.42.674 3.08.675 4.5.001v8.755h.75a.75.75 0 010 1.5H2.25a.75.75 0 010-1.5H3zm3-6a.75.75 0 01.75-.75h3a.75.75 0 01.75.75v3a.75.75 0 01-.75.75h-3a.75.75 0 01-.75-.75v-3zm8.25-.75a.75.75 0 00-.75.75v5.25c0 .414.336.75.75.75h3a.75.75 0 00.75-.75v-5.25a.75.75 0 00-.75-.75h-3z" clip-rule="evenodd" />
</svg>

                          </span>
                        </div>
                        <h3 class="mt-3 text-lg font-medium leading-6 text-gray-900">{{ $project->title }}</h3>
                        <p class="mt-3 text-sm text-gray-500">{{ __("frontend.{$project->type->value}") }}</p>
                    </div>
                </li>
            @endforeach

            <li class="col-span-1">
                <a href="{{ route('projects.create') }}" class="relative block w-full rounded-lg border-2 border-dashed border-gray-300 p-12 text-center text-amber-400 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2">
                    <svg class="mx-auto h-12 w-12" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 10.5v6m3-3H9m4.06-7.19l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z" />
                    </svg>

                    <span class="mt-3 block text-sm font-medium text-gray-900">{{ __('Create new project') }}</span>
                </a>

            </li>

            <!-- More people... -->
        </ul>


    </x-main-container>

</x-app-layout>
