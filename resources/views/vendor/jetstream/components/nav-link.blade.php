@props(['active', 'sub'])

@php

$classes = 'inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium leading-5 focus:outline-none transition';

$classes = ($active ?? false)
            ? (($sub ?? false)
                ? "{$classes} border-amber-400 focus:border-amber-700 text-gray-900"
                : "{$classes} border-indigo-400 focus:border-indigo-700 text-gray-900")
            : "{$classes} border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 focus:text-gray-700 focus:border-gray-300";
@endphp

<a {{ $attributes->merge(['class' => $classes]) }}>
    {{ $slot }}
</a>
