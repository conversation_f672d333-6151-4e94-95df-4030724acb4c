definitions:
  services:
    docker:
      memory: 2048

pipelines:
  custom:
    deployment-to-prod:
      - step:
          name: Build
          services: [ docker ]
          runs-on: [ self.hosted ]
          clone:
            depth: 1
          script:
            - make
            - make push
      - step:
          name: Deploy to Production
          deployment: production
          image: janniet/build-pipe
          runs-on: [ self.hosted ]
          script:
            - make deploy-production
    deployment-to-private:
      - step:
          name: Build
          services: [ docker ]
          runs-on: [ self.hosted ]
          clone:
            depth: 1
          script:
            - make
            - make push
      - step:
          name: Deploy to Private
          deployment: private
          image: janniet/build-pipe
          runs-on: [ self.hosted ]
          script:
            - make deploy-production
  branches:
    master:
      - step:
          name: Build
          services: [ docker ]
          runs-on: [ self.hosted ]
          clone:
            depth: 1
          script:
            - make
            - make push
    develop:
      - step:
          name: Build
          services: [docker]
          runs-on:  [self.hosted]
          clone:
            depth: 1
          script:
            - make
            - make push
      - step:
          name: Deploy to TEST
          deployment: test
          image: janniet/build-pipe
          runs-on:  [self.hosted]
          clone:
            depth: 1
          script:
            - make deploy

